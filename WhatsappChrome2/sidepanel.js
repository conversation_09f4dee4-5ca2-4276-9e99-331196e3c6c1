// Script para o side panel da extensão WhatsApp Suporte

document.addEventListener('DOMContentLoaded', function() {
    // Elementos da interface
    const statusIndicator = document.getElementById('status-indicator');
    const statusMessage = document.getElementById('status-message');
    const openWhatsAppButton = document.getElementById('open-whatsapp');
    const settingsButton = document.getElementById('settings-button');
    const findPromoKitButton = document.getElementById('find-promokit');
    const syncDataButton = document.getElementById('sync-data');
    const messagesCount = document.getElementById('messages-count');
    const clientsCount = document.getElementById('clients-count');
    const pendingCount = document.getElementById('pending-count');
    const promokitTabsList = document.getElementById('promokit-tabs-list');
    const recentMessages = document.getElementById('recent-messages');
    const lastSyncTime = document.getElementById('last-sync-time');
    
    // Verifica o estado inicial da extensão
    checkExtensionStatus();
    loadPromoKitTabs();
    loadRecentMessages();
    updateLastSyncTime();
    
    // Adiciona eventos aos botões
    openWhatsAppButton.addEventListener('click', openWhatsAppWeb);
    settingsButton.addEventListener('click', openSettings);
    findPromoKitButton.addEventListener('click', findPromoKitTabs);
    syncDataButton.addEventListener('click', syncData);
    
    // Recebe eventos de contato selecionado
    chrome.runtime.onMessage.addListener(function(msg) {
        if (msg.type === 'SELECIONOU_CONTATO') {
            console.log('Sidepanel: recebeu SELECIONOU_CONTATO', msg.payload);
            alert('Contato selecionado2: ' + (msg.payload.nome || msg.payload.url));
        }
    });
    
    // Verifica o status da extensão
    function checkExtensionStatus() {
        chrome.storage.local.get(['isActive', 'unreadMessages', 'lastSync'], function(data) {
            if (data.isActive) {
                statusIndicator.classList.remove('status-inactive');
                statusIndicator.classList.add('status-active');
                statusMessage.textContent = 'Conectado ao WhatsApp Web';
            } else {
                statusIndicator.classList.remove('status-active');
                statusIndicator.classList.add('status-inactive');
                statusMessage.textContent = 'Aguardando conexão com WhatsApp Web...';
            }
            
            updateCounters();
        });
    }
    
    // Abre o WhatsApp Web em uma nova aba
    function openWhatsAppWeb() {
        chrome.tabs.create({ url: 'https://web.whatsapp.com/' });
    }
    
    // Abre a página de configurações da extensão
    function openSettings() {
        chrome.runtime.openOptionsPage();
    }
    
    // Busca e exibe abas PromoKit
    function findPromoKitTabs() {
        chrome.runtime.sendMessage({ action: 'findPromoKitTabs' }, function(response) {
            if (response && response.tabs) {
                displayPromoKitTabs(response.tabs);
            }
        });
    }
    
    // Carrega abas PromoKit automaticamente
    function loadPromoKitTabs() {
        findPromoKitTabs();
    }
    
    // Exibe as abas PromoKit encontradas
    function displayPromoKitTabs(tabs) {
        if (tabs.length === 0) {
            promokitTabsList.innerHTML = '<div class="no-tabs-message">Nenhuma aba PromoKit encontrada</div>';
            return;
        }
        
        promokitTabsList.innerHTML = '';
        
        tabs.forEach(tab => {
            const tabElement = document.createElement('div');
            tabElement.className = 'tab-item';
            
            const url = new URL(tab.url);
            const domain = url.hostname;
            
            tabElement.innerHTML = `
                <div class="tab-info">
                    <div class="tab-title">${tab.title}</div>
                    <div class="tab-url">${domain}</div>
                </div>
                <div class="tab-actions">
                    <button class="tab-btn" data-tab-id="${tab.id}" title="Ir para aba">
                        <i class="fas fa-external-link-alt"></i>
                    </button>
                </div>
            `;
            
            // Adiciona evento para ir para a aba
            const tabBtn = tabElement.querySelector('.tab-btn');
            tabBtn.addEventListener('click', () => {
                chrome.tabs.update(tab.id, { active: true });
                chrome.windows.update(tab.windowId, { focused: true });
            });
            
            promokitTabsList.appendChild(tabElement);
        });
    }
    
    // Sincroniza dados com o servidor
    function syncData() {
        syncDataButton.disabled = true;
        syncDataButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>Sincronizando...</span>';
        
        chrome.runtime.sendMessage({ 
            type: 'SYNC_DATA', 
            data: { timestamp: new Date().toISOString() }
        }, function(response) {
            syncDataButton.disabled = false;
            syncDataButton.innerHTML = '<i class="fas fa-sync-alt"></i><span>Sincronizar Dados</span>';
            
            if (response && response.status === 'synced') {
                updateLastSyncTime();
                checkExtensionStatus();
            }
        });
    }
    
    // Atualiza os contadores de estatísticas
    function updateCounters() {
        chrome.storage.local.get(['unreadMessages', 'clientCount', 'pendingCount'], function(data) {
            messagesCount.textContent = data.unreadMessages || 0;
            clientsCount.textContent = data.clientCount || 0;
            pendingCount.textContent = data.pendingCount || 0;
        });
        
        // Verifica se há uma aba do WhatsApp Web aberta
        chrome.tabs.query({ url: 'https://web.whatsapp.com/*' }, function(tabs) {
            if (tabs.length > 0) {
                statusIndicator.classList.remove('status-inactive');
                statusIndicator.classList.add('status-active');
                statusMessage.textContent = 'Conectado ao WhatsApp Web';
                
                // Envia mensagem para verificar o status real da conexão
                chrome.tabs.sendMessage(tabs[0].id, { action: 'CHECK_STATUS' }, function(response) {
                    if (chrome.runtime.lastError || !response) {
                        return;
                    }
                    
                    if (response.connected) {
                        statusMessage.textContent = 'Conectado ao WhatsApp Web';
                    } else {
                        statusMessage.textContent = 'WhatsApp Web aberto, mas não conectado';
                    }
                });
            }
        });
    }
    
    // Carrega mensagens recentes
    function loadRecentMessages() {
        chrome.storage.local.get(['recentMessages'], function(data) {
            if (data.recentMessages && data.recentMessages.length > 0) {
                displayRecentMessages(data.recentMessages);
            } else {
                recentMessages.innerHTML = '<div class="no-messages">Nenhuma mensagem recente</div>';
            }
        });
    }
    
    // Exibe mensagens recentes
    function displayRecentMessages(messages) {
        recentMessages.innerHTML = '';
        
        messages.slice(0, 5).forEach(message => {
            const messageElement = document.createElement('div');
            messageElement.className = 'message-item';
            
            const time = new Date(message.timestamp).toLocaleTimeString('pt-BR', {
                hour: '2-digit',
                minute: '2-digit'
            });
            
            messageElement.innerHTML = `
                <div class="message-info">
                    <div class="message-contact">${message.contact}</div>
                    <div class="message-preview">${message.text}</div>
                </div>
                <div class="message-time">${time}</div>
            `;
            
            recentMessages.appendChild(messageElement);
        });
    }
    
    // Atualiza o horário da última sincronização
    function updateLastSyncTime() {
        chrome.storage.local.get(['lastSync'], function(data) {
            if (data.lastSync) {
                const syncDate = new Date(data.lastSync);
                const timeString = syncDate.toLocaleString('pt-BR');
                lastSyncTime.textContent = `Última sincronização: ${timeString}`;
            } else {
                lastSyncTime.textContent = 'Última sincronização: Nunca';
            }
        });
    }
    
    // Verifica o status periodicamente
    setInterval(checkExtensionStatus, 10000);
    
    // Atualiza abas PromoKit periodicamente
    setInterval(loadPromoKitTabs, 30000);
    
    // Atualiza mensagens recentes periodicamente
    setInterval(loadRecentMessages, 15000);
    
    // Listener para mudanças no storage
    chrome.storage.onChanged.addListener(function(changes, namespace) {
        if (namespace === 'local') {
            if (changes.unreadMessages || changes.clientCount || changes.pendingCount) {
                updateCounters();
            }
            if (changes.recentMessages) {
                loadRecentMessages();
            }
            if (changes.lastSync) {
                updateLastSyncTime();
            }
        }
    });
    
    // Chave de armazenamento para URL do iframe
    const storageKey = 'iframeCustomUrl';
    
    // Funções do iframe settings
    function showNotification(message, type = 'success') {
        if (!notification) return;
        const icon = type === 'success' ? '<i class="fas fa-check-circle"></i>' : '<i class="fas fa-exclamation-circle"></i>';
        notification.innerHTML = `${icon} ${message}`;
        notification.className = `notification ${type}`;
        notification.style.display = 'flex';
        setTimeout(() => {
            notification.style.display = 'none';
        }, 5000);
    }
    
    function formatPromokitUrl(url) {
        try {
            const urlObj = new URL(url);
            if (urlObj.hostname.endsWith('promokit.com.br')) {
                return `https://${urlObj.hostname}/admin/index`;
            }
            return url;
        } catch (e) {
            return url;
        }
    }
    
    function filterTabs(event) {
        const searchTerm = event.target.value.toLowerCase();
        const tabItems = document.querySelectorAll('#tabsList .tab-item');
        let visibleCount = 0;

        tabItems.forEach(item => {
            const title = item.querySelector('.tab-title')?.textContent.toLowerCase() || '';
            const url = item.querySelector('.tab-url')?.textContent.toLowerCase() || '';

            if (title.includes(searchTerm) || url.includes(searchTerm)) {
                item.style.display = 'flex';
                visibleCount++;
            } else {
                item.style.display = 'none';
            }
        });

        if (visibleCount === 0 && tabItems.length > 0 && noTabsMessage) {
            noTabsMessage.style.display = 'flex';
            noTabsMessage.innerHTML = `<i class="fas fa-search"></i> Nenhuma página encontrada para "${searchTerm}"`;
        } else if (noTabsMessage) {
            noTabsMessage.style.display = 'none';
        }
    }
    
    function showManualEntry() {
        if (iframeSettingsForm) {
            iframeSettingsForm.style.display = 'block';
            if (iframeUrl) {
                iframeUrl.disabled = false;
                iframeUrl.focus();
            }
            if (urlHelpText) {
                urlHelpText.textContent = 'Insira manualmente a URL completa incluindo https://';
            }
        }
    }
    
    function cancelIframeSettings() {
        if (iframeSettingsForm) {
            iframeSettingsForm.style.display = 'none';
        }
        
        chrome.storage.local.get([storageKey], function(result) {
            if (result[storageKey] && iframeUrl) {
                iframeUrl.value = result[storageKey];
            } else if (iframeUrl) {
                iframeUrl.value = '';
            }
        });
        
        document.querySelectorAll('#tabsList .tab-item').forEach(item => {
            item.classList.remove('selected');
            const btn = item.querySelector('.btn-select');
            if (btn) {
                btn.textContent = 'Selecionar';
                btn.classList.remove('selected');
            }
        });
        
        if (urlHelpText) {
            urlHelpText.textContent = 'Insira a URL completa incluindo https://';
        }
    }
    
    function saveIframeUrl(event) {
        event.preventDefault();
        if (!iframeUrl) return;
        
        let newUrl = iframeUrl.value.trim();
        
        if (newUrl) {
            try {
                newUrl = formatPromokitUrl(newUrl);
                iframeUrl.value = newUrl;
                
                new URL(newUrl);
                chrome.storage.local.set({ [storageKey]: newUrl }, function() {
                    if (chrome.runtime.lastError) {
                        console.error('Erro ao salvar URL:', chrome.runtime.lastError);
                        showNotification(`Erro ao salvar: ${chrome.runtime.lastError.message}`, 'error');
                    } else {
                        console.log('URL salva:', newUrl);
                        showNotification('URL salva com sucesso!', 'success');
                        
                        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
                            if (tabs[0]) {
                                chrome.tabs.sendMessage(tabs[0].id, {
                                    type: 'IFRAME_URL_UPDATED',
                                    url: newUrl
                                });
                            }
                        });
                        
                        chrome.runtime.sendMessage({
                            type: 'IFRAME_URL_UPDATED',
                            url: newUrl
                        });
                        
                        if (iframeSettingsForm) {
                            iframeSettingsForm.style.display = 'none';
                        }
                    }
                });
            } catch (error) {
                showNotification('URL inválida', 'error');
            }
        } else {
            showNotification('Por favor, insira uma URL válida', 'error');
        }
    }
    
    function findPromokitTabsForIframe() {
        if (!findTabsBtn || !tabsContainer || !tabsList || !noTabsMessage) return;
        
        findTabsBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Buscando...';
        findTabsBtn.disabled = true;
        
        tabsList.innerHTML = '';
        tabsContainer.style.display = 'block';
        noTabsMessage.style.display = 'flex';
        noTabsMessage.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Buscando páginas abertas...';
        
        if (tabCountBadge) {
            tabCountBadge.style.display = 'none';
        }
        
        const searchContainer = tabsContainer.querySelector('.search-container');
        if (searchContainer) {
            searchContainer.style.display = 'none';
        }
        
        const existingInstruction = tabsContainer.querySelector('.instruction-box');
        if (existingInstruction) {
            existingInstruction.remove();
        }
        
        const existingStatus = document.getElementById('selectionStatus');
        if (existingStatus) {
            existingStatus.remove();
        }
        
        chrome.tabs.query({}, function(tabs) {
            const promokitTabs = tabs.filter(tab => {
                try {
                    const url = new URL(tab.url);
                    return url.protocol === 'https:' && url.hostname.endsWith('promokit.com.br');
                } catch (e) {
                    return false;
                }
            });
            
            setTimeout(() => {
                findTabsBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Buscar páginas do Promokit';
                findTabsBtn.disabled = false;
                
                if (promokitTabs.length > 0) {
                    noTabsMessage.style.display = 'none';
                    
                    if (iframeUrl) {
                        iframeUrl.disabled = true;
                        iframeUrl.placeholder = 'Selecione uma página da lista acima';
                    }
                    if (urlHelpText) {
                        urlHelpText.textContent = 'Clique em uma página acima ou insira manualmente';
                    }
                    
                    if (tabCountBadge) {
                        tabCountBadge.textContent = promokitTabs.length;
                        tabCountBadge.style.display = 'inline-flex';
                    }
                    
                    const searchContainer = tabsContainer.querySelector('.search-container');
                    if (searchContainer && promokitTabs.length > 1) {
                        searchContainer.style.display = 'block';
                    }
                    
                    if (tabSearchInput) {
                        tabSearchInput.value = '';
                    }
                    
                    const instructionBox = document.createElement('div');
                    instructionBox.className = 'instruction-box';
                    instructionBox.innerHTML = '<i class="fas fa-info-circle"></i><span>A primeira página será selecionada automaticamente. Você pode escolher outra clicando nela.</span>';
                    tabsContainer.insertBefore(instructionBox, tabsList);
                    
                    let firstTabItem = null;
                    let firstTab = null;
                    
                    promokitTabs.forEach((tab, index) => {
                        const tabItem = document.createElement('div');
                        tabItem.className = 'tab-item';
                        
                        if (index === 0) {
                            tabItem.classList.add('highlighted');
                            firstTabItem = tabItem;
                            firstTab = tab;
                        }
                        
                        const displayUrl = tab.url.length > 60 ?
                            tab.url.substring(0, 57) + '...' :
                            tab.url;
                        
                        tabItem.innerHTML = `
                            <div class="tab-content">
                                <div class="tab-title"><span class="tab-index">${index + 1}.</span> ${tab.title}</div>
                                <div class="tab-url" title="${tab.url}">${displayUrl}</div>
                            </div>
                            <button class="btn-select">Selecionar</button>
                        `;
                        
                        const selectTab = function() {
                            if (iframeSettingsForm) {
                                iframeSettingsForm.style.display = 'block';
                            }
                            
                            const formattedUrl = formatPromokitUrl(tab.url);
                            
                            if (iframeUrl) {
                                iframeUrl.disabled = false;
                                iframeUrl.value = formattedUrl;
                            }
                            if (urlHelpText) {
                                urlHelpText.textContent = 'URL formatada para /admin/index. Clique em Salvar para confirmar.';
                            }
                            showNotification('URL formatada para o padrão correto', 'success');
                            
                            document.querySelectorAll('#tabsList .tab-item').forEach(item => {
                                item.classList.remove('selected');
                                const btn = item.querySelector('.btn-select');
                                if (btn) {
                                    btn.textContent = 'Selecionar';
                                    btn.classList.remove('selected');
                                }
                            });
                            tabItem.classList.add('selected');
                            
                            const selectedBtn = tabItem.querySelector('.btn-select');
                            if (selectedBtn) {
                                selectedBtn.textContent = 'Selecionada';
                                selectedBtn.classList.add('selected');
                            }
                            
                            const selectionStatus = document.getElementById('selectionStatus');
                            if (selectionStatus) {
                                selectionStatus.className = 'selection-status selected';
                                selectionStatus.innerHTML = '<i class="fas fa-check-circle"></i><span>Página selecionada: ' + tab.title + '</span>';
                            }
                        };
                        
                        tabItem.addEventListener('click', selectTab);
                        
                        const selectButton = tabItem.querySelector('.btn-select');
                        if (selectButton) {
                            selectButton.addEventListener('click', function(e) {
                                e.stopPropagation();
                                selectTab();
                            });
                        }
                        
                        tabsList.appendChild(tabItem);
                    });
                    
                    const selectionStatus = document.createElement('div');
                    selectionStatus.id = 'selectionStatus';
                    selectionStatus.className = 'selection-status';
                    selectionStatus.innerHTML = '<i class="fas fa-info-circle"></i><span>Nenhuma página selecionada. Clique em uma página acima para selecioná-la.</span>';
                    tabsContainer.appendChild(selectionStatus);
                    
                    if (firstTabItem && firstTab) {
                        setTimeout(() => {
                            if (iframeSettingsForm) {
                                iframeSettingsForm.style.display = 'block';
                            }
                            
                            const formattedUrl = formatPromokitUrl(firstTab.url);
                            
                            if (iframeUrl) {
                                iframeUrl.disabled = false;
                                iframeUrl.value = formattedUrl;
                            }
                            if (urlHelpText) {
                                urlHelpText.textContent = 'URL formatada para /admin/index. Clique em Salvar para confirmar.';
                            }
                            
                            document.querySelectorAll('#tabsList .tab-item').forEach(item => {
                                item.classList.remove('selected');
                                const btn = item.querySelector('.btn-select');
                                if (btn) {
                                    btn.textContent = 'Selecionar';
                                    btn.classList.remove('selected');
                                }
                            });
                            firstTabItem.classList.add('selected');
                            
                            const selectedBtn = firstTabItem.querySelector('.btn-select');
                            if (selectedBtn) {
                                selectedBtn.textContent = 'Selecionada';
                                selectedBtn.classList.add('selected');
                            }
                            
                            selectionStatus.className = 'selection-status selected';
                            selectionStatus.innerHTML = '<i class="fas fa-check-circle"></i><span>Página selecionada: ' + firstTab.title + '</span>';
                            
                            const instructionBox = tabsContainer.querySelector('.instruction-box');
                            if (instructionBox) {
                                instructionBox.innerHTML = '<i class="fas fa-info-circle"></i><span>Página selecionada automaticamente. Você pode escolher outra ou clicar em Salvar.</span>';
                            }
                        }, 100);
                    }
                } else {
                    noTabsMessage.style.display = 'flex';
                    noTabsMessage.innerHTML = '<i class="fas fa-exclamation-circle"></i> Nenhuma página do promokit.com.br encontrada.';
                    
                    if (tabCountBadge) {
                        tabCountBadge.style.display = 'none';
                    }
                    
                    if (iframeUrl) {
                        iframeUrl.disabled = false;
                        iframeUrl.placeholder = 'https://exemplo.promokit.com.br';
                        iframeUrl.focus();
                    }
                    if (urlHelpText) {
                        urlHelpText.textContent = 'Insira a URL completa incluindo https://';
                    }
                    
                    const instructionBox = document.createElement('div');
                    instructionBox.className = 'instruction-box';
                    instructionBox.innerHTML = '<i class="fas fa-info-circle"></i><span>Nenhuma página encontrada. Insira a URL manualmente abaixo.</span>';
                    tabsContainer.appendChild(instructionBox);
                    
                    if (iframeSettingsForm) {
                        iframeSettingsForm.style.display = 'block';
                    }
                }
            }, 800);
        });
    }
    
    // Carregar URL salva ao iniciar
    chrome.storage.local.get([storageKey], function(result) {
        if (result[storageKey] && iframeUrl) {
            iframeUrl.value = result[storageKey];
            chrome.runtime.sendMessage({
                type: 'IFRAME_URL_UPDATED',
                url: result[storageKey]
            });
        }
    });
    
    // Referências para elementos do iframe-settings
    const notification = document.getElementById('notification');
    const iframeUrl = document.getElementById('iframeUrl');
    const urlHelpText = document.getElementById('urlHelpText');
    const iframeSettingsForm = document.getElementById('iframeSettingsForm');
    const findTabsBtn = document.getElementById('findTabsBtn');
    const tabsContainer = document.getElementById('tabsContainer');
    const tabsList = document.getElementById('tabsList');
    const noTabsMessage = document.getElementById('noTabsMessage');
    const tabCountBadge = document.getElementById('tabCountBadge');
    const tabSearchInput = document.getElementById('tabSearchInput');
    
    // Inicializar campo como habilitado
    if (iframeUrl) {
        iframeUrl.disabled = false;
        iframeUrl.placeholder = 'https://exemplo.promokit.com.br';
    }
});