// Content script injetado nas páginas *.promokit.com.br
// Faz a ponte entre a página PromoKit (Angular) e a extensão.

// Estado da conversa atual
let contextoAtual = {
  mensagens: [],
  contatoAtual: '',
  telefoneAtual: '',
  etapaFunil: 'Prospecção',
  tomConversa: 'Consultivo'
};

// 1. Mensagens vindas do background / sidepanel → página
chrome.runtime.onMessage.addListener((msg) => {
  console.log('Mensagem recebida no content-promokit:', msg);
  
  // Quando um contato é selecionado no WhatsApp
  if (msg.type === 'SELECIONOU_CONTATO') {
    // Atualiza informações do contato no contexto
    contextoAtual.contatoAtual = msg.payload.nome || '';
    contextoAtual.telefoneAtual = msg.payload.telefone || '';
    
    // Reenvia para o contexto da página (AppComponent já escuta window.onmessage)
    window.postMessage({ tipo: 'SELECIONOU_CONTATO', payload: msg.payload }, '*');
    
    // Também envia o contexto atualizado para o CRM
    enviarContextoParaCRM();
  }
  
  // Quando novas mensagens são detectadas no WhatsApp
  if (msg.type === 'NOVAS_MENSAGENS') {
    // Atualiza o histórico de mensagens no contexto
    if (msg.payload && Array.isArray(msg.payload.mensagens)) {
      contextoAtual.mensagens = msg.payload.mensagens;
      enviarContextoParaCRM();
    }
  }
});

// 2. Mensagens da página → background (se necessário)
window.addEventListener('message', (e) => {
  if (!e.data || !e.data.tipo) return;
  
  // Página quer solicitar algo à extensão
  if (e.data.tipo === 'PROMOKIT_TO_EXTENSION') {
    chrome.runtime.sendMessage(e.data.msg);
  }
  
  // Atualização da etapa do funil ou tom da conversa
  if (e.data.tipo === 'ATUALIZAR_CONTEXTO') {
    if (e.data.payload) {
      // Atualiza apenas os campos fornecidos
      if (e.data.payload.etapaFunil) {
        contextoAtual.etapaFunil = e.data.payload.etapaFunil;
      }
      if (e.data.payload.tomConversa) {
        contextoAtual.tomConversa = e.data.payload.tomConversa;
      }
      console.log('Contexto atualizado:', contextoAtual);
    }
  }
  
  // Requisição para enviar mensagem para o WhatsApp
  if (e.data.tipo === 'ENVIAR_MENSAGEM_WHATSAPP') {
    if (e.data.payload && e.data.payload.texto) {
      chrome.runtime.sendMessage({
        tipo: 'ENVIAR_TEXTO_WHATSAPP',
        texto: e.data.payload.texto
      });
      
      // Adiciona a mensagem enviada ao contexto
      const novaMensagem = {
        texto: e.data.payload.texto,
        remetente: 'Eu',
        horario: new Date().toLocaleTimeString(),
        tipo: 'saida'
      };
      contextoAtual.mensagens.push(novaMensagem);
      enviarContextoParaCRM();
    }
  }
});

/**
 * Função que envia o contexto atual da conversa para o CRM
 */
function enviarContextoParaCRM() {
  window.postMessage({
    type: 'crm_conversa_atualizada',
    payload: contextoAtual
  }, '*');
  console.log('Contexto enviado para CRM:', contextoAtual);
}

/**
 * Função que coleta mensagens do DOM do WhatsApp
 * Será chamada periodicamente para monitorar novas mensagens
 */
function coletarMensagensWhatsApp() {
  // Esse código será implementado para interagir com o DOM do WhatsApp Web
  // Para fins de exemplo, simularemos mensagens
  console.log('Monitorando mensagens do WhatsApp...');
}

// Inicia o monitoramento de mensagens (a cada 2 segundos)
setInterval(coletarMensagensWhatsApp, 2000);

console.log('PromoKit content-script carregado e bridge estabelecida.');
