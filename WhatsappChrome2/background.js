// Copyright 2024 MeuCardapio
// WhatsApp Suporte - Background script
// Este script roda em segundo plano e gerencia o side panel para WhatsApp Web

chrome.runtime.onMessage.addListener((msg, sender, send) => {
  if (msg.cmd !== "igProfile") return;

  debugger;
  console.log('msg ', msg);

  fetch(
    `https://i.instagram.com/api/v1/users/web_profile_info2/?username=${msg.user}`,
    {
      headers: {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 Safari/537.36",
        "X-IG-App-ID": "936619743392459",
        "Accept": "*/*",
        "Accept-Language": "en-US,en;q=0.9",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache",
        "Sec-<PERSON>tch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-site"
      }
    }
  )
    .then(r => {
      debugger;
      return r.json();
    })
    .then(json => send({ ok: true, data: json }))
    .catch(err => send({ ok: false, err: err + "" }));
  return true; // responde async
});

// Configuração inicial quando a extensão é instalada ou atualizada
chrome.runtime.onInstalled.addListener(() => {
  console.log('Extensão WhatsApp Suporte instalada/atualizada');

  // Inicializa o armazenamento com valores padrão
  chrome.storage.local.set({
    isActive: false,
    unreadMessages: 0,
    lastSync: null,
    userSettings: {
      autoReply: false,
      autoReplyMessage: "Olá! Obrigado por entrar em contato. Um atendente irá responder em breve.",
      workingHours: {
        start: "09:00",
        end: "18:00"
      }
    }
  });
});

// Listener para mensagens do content script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'NEW_MESSAGE') {
    // Processa nova mensagem recebida
    handleNewMessage(message.data);
    sendResponse({ status: 'received' });
  } else if (message.type === 'SYNC_DATA') {
    // Sincroniza dados com o servidor
    syncWithServer(message.data)
      .then(result => sendResponse({ status: 'synced', data: result }))
      .catch(error => sendResponse({ status: 'error', error }));
    return true; // Mantém a conexão aberta para resposta assíncrona
  } else if (message.type === 'GET_SETTINGS') {
    // Retorna configurações atuais
    chrome.storage.local.get('userSettings', (data) => {
      sendResponse({ settings: data.userSettings });
    });
    return true; // Mantém a conexão aberta para resposta assíncrona
  } else if (message.action === 'findPromoKitTabs') {
    // Nova funcionalidade: Buscar abas do PromoKit
    findPromoKitTabs(sendResponse);
    return true; // Mantém a conexão aberta para resposta assíncrona
  } else if (message.tipo === 'ENVIAR_TEXTO_INSTAGRAM') {
    // Envia mensagem para o Instagram
    handleInstagramMessage(message, sender);
    sendResponse({ status: 'instagram_message_sent' });
  } else if (message.type === 'NOVAS_MENSAGENS_INSTAGRAM') {
    // Processa novas mensagens do Instagram
    handleInstagramMessages(message.data);
    sendResponse({ status: 'instagram_messages_received' });
  }
});

// Função para buscar abas com domínio promokit.com.br
function findPromoKitTabs(sendResponse) {
  // Buscar todas as abas abertas
  chrome.tabs.query({}, function(tabs) {
    // Filtrar abas com o domínio promokit.com.br
    const promoKitTabs = tabs.filter(tab => {
      try {
        const url = new URL(tab.url);
        return url.hostname.endsWith('.promokit.com.br');
      } catch (e) {
        return false;
      }
    });

    // Enviar resposta com as abas encontradas
    sendResponse({ tabs: promoKitTabs });
  });
}

// Gerencia novas mensagens recebidas
function handleNewMessage(messageData) {
  // Incrementa contador de mensagens não lidas
  chrome.storage.local.get('unreadMessages', (data) => {
    const count = (data.unreadMessages || 0) + 1;
    chrome.storage.local.set({ unreadMessages: count });

    // Atualiza o badge da extensão
    if (chrome.action && chrome.action.setBadgeText) {
      chrome.action.setBadgeText({ text: count.toString() });
      chrome.action.setBadgeBackgroundColor({ color: '#4a6bff' });
    }
    // Código de notificações removido pois não é necessário
  });
}

// Sincroniza dados com o servidor
async function syncWithServer(data) {
  try {
    // Implementação da sincronização com o servidor
    const response = await fetch('https://api.meucardapio.com/whatsapp-sync', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error('Falha na sincronização');
    }

    const result = await response.json();

    // Atualiza timestamp da última sincronização
    chrome.storage.local.set({ lastSync: new Date().toISOString() });

    return result;
  } catch (error) {
    console.error('Erro na sincronização:', error);
    throw error;
  }
}

// Função para lidar com mensagens do Instagram
function handleInstagramMessage(message, sender) {
  console.log('Enviando mensagem para Instagram:', message.texto);
  
  // Aqui você pode implementar a lógica específica para enviar mensagens no Instagram
  // Por exemplo, injetar um script para digitar no campo de mensagem
  if (sender.tab && sender.tab.id) {
    chrome.tabs.sendMessage(sender.tab.id, {
      type: 'EXECUTE_INSTAGRAM_SEND',
      texto: message.texto
    });
  }
}

// Função para processar mensagens do Instagram
function handleInstagramMessages(messageData) {
  console.log('Processando mensagens do Instagram:', messageData);
  
  // Incrementa contador de mensagens não lidas do Instagram
  chrome.storage.local.get('instagramUnreadMessages', (data) => {
    const count = (data.instagramUnreadMessages || 0) + (messageData.mensagens ? messageData.mensagens.length : 1);
    chrome.storage.local.set({ instagramUnreadMessages: count });
    
    // Atualiza o badge da extensão incluindo mensagens do Instagram
    chrome.storage.local.get('unreadMessages', (whatsappData) => {
      const totalCount = (whatsappData.unreadMessages || 0) + count;
      if (chrome.action && chrome.action.setBadgeText) {
        chrome.action.setBadgeText({ text: totalCount.toString() });
        chrome.action.setBadgeBackgroundColor({ color: '#e91e63' }); // Rosa para Instagram
      }
    });
  });
}

// Código de alarme removido pois não é necessário

// User Agent personalizado
const CUSTOM_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 Safari/537.36";

// Configura regras para modificar User-Agent usando declarativeNetRequest
chrome.runtime.onInstalled.addListener(() => {
  chrome.declarativeNetRequest.updateDynamicRules({
    removeRuleIds: [1], // Remove regra anterior se existir
    addRules: [{
      id: 1,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [{
          header: "user-agent",
          operation: "set",
          value: CUSTOM_USER_AGENT
        }]
      },
      condition: {
        urlFilter: "*",
        resourceTypes: ["main_frame", "sub_frame", "xmlhttprequest", "other"]
      }
    }]
  });
});

// Monitora mudanças de URL no Instagram usando webNavigation API
chrome.webNavigation.onHistoryStateUpdated.addListener((details) => {
  // Só processa se for uma página do Instagram
  if (details.url.includes('instagram.com')) {
    const isCrmPage = details.url.includes('/crm/home');

    // Envia mensagem para o content script do Instagram
    chrome.tabs.sendMessage(details.tabId, {
      type: 'URL_CHANGED',
      url: details.url,
      isCrmPage: isCrmPage
    }).catch(error => {
      // Ignora erros se o content script não estiver carregado ainda
      console.log('Content script não disponível ainda:', error);
    });
  }
});
