/**
 * Teste da lógica de análise de sócio principal
 * Executa: node test-socio-principal.js
 */

// Simular a função analisarSocioPrincipal do backend
function analisarSocioPrincipal(socios, razaoSocial) {
  if (!socios || socios.length === 0) return socios;
  
  console.log('🔍 Analisando sócio principal entre', socios.length, 'sócios...');
  
  // Se há apenas 1 sócio, é automaticamente o principal
  if (socios.length === 1) {
    socios[0].principal = true;
    socios[0].scoreAnalise = 100;
    socios[0].motivoSelecao = 'Sócio único da empresa';
    console.log('✅ Sócio único identificado:', socios[0].nome);
    return socios;
  }

  // Calcular score para cada sócio
  socios.forEach((socio, index) => {
    let score = 0;
    let motivos = [];
    
    // 1. CRITÉRIO: Cargo/Qualificação (peso 40)
    const cargo = (socio.cargo || '').toLowerCase();
    if (cargo.includes('diretor') || cargo.includes('presidente') || cargo.includes('ceo') || 
        cargo.includes('administrador') || cargo.includes('proprietário')) {
      score += 40;
      motivos.push('Cargo de direção');
    } else if (cargo.includes('gerente') || cargo.includes('sócio-gerente') || 
               cargo.includes('sócio gerente')) {
      score += 25;
      motivos.push('Cargo gerencial');
    } else if (cargo.includes('administrador') || cargo.includes('responsável')) {
      score += 20;
      motivos.push('Cargo administrativo');
    } else {
      score += 10; // Sócio comum
    }

    // 2. CRITÉRIO: Posição na lista (peso 20)
    if (index === 0) {
      score += 20;
      motivos.push('Primeiro na lista');
    } else if (index === 1) {
      score += 10;
    } else {
      score += Math.max(0, 10 - index);
    }

    // 3. CRITÉRIO: Similaridade com razão social (peso 20)
    if (razaoSocial && socio.nome) {
      const nomeCompleto = socio.nome.toLowerCase();
      const razaoLower = razaoSocial.toLowerCase();
      
      const palavrasNome = nomeCompleto.split(' ').filter(p => p.length > 2);
      const palavrasRazao = razaoLower.split(' ');
      
      let palavrasEncontradas = 0;
      palavrasNome.forEach(palavra => {
        if (palavrasRazao.some(r => r.includes(palavra) || palavra.includes(r))) {
          palavrasEncontradas++;
        }
      });
      
      if (palavrasEncontradas > 0) {
        const similaridade = (palavrasEncontradas / palavrasNome.length) * 20;
        score += similaridade;
        if (similaridade > 10) {
          motivos.push('Nome similar à empresa');
        }
      }
    }

    // 4. CRITÉRIO: Data de entrada (peso 20)
    if (socio.dataEntrada) {
      score += 15; // Simplificado para teste
      motivos.push('Sócio experiente');
    } else {
      score += 10;
    }

    socio.scoreAnalise = Math.round(score);
    socio.motivoSelecao = motivos.join(', ');
    socio.principal = false;
    
    console.log(`📊 ${socio.nome}: Score ${socio.scoreAnalise} (${socio.motivoSelecao})`);
  });

  // Encontrar sócio com maior score
  const socioComMaiorScore = socios.reduce((melhor, atual) => 
    atual.scoreAnalise > melhor.scoreAnalise ? atual : melhor
  );

  socioComMaiorScore.principal = true;
  
  console.log(`🎯 Sócio principal selecionado: ${socioComMaiorScore.nome} (Score: ${socioComMaiorScore.scoreAnalise})`);
  console.log(`   Motivos: ${socioComMaiorScore.motivoSelecao}`);

  return socios;
}

// ===== CASOS DE TESTE =====

console.log('='.repeat(80));
console.log('TESTE 1: Sócio único');
console.log('='.repeat(80));

let teste1 = [
  { nome: 'João Silva', cargo: 'Sócio', dataEntrada: '01/01/2020' }
];

analisarSocioPrincipal(teste1, 'JOÃO SILVA RESTAURANTES LTDA');

console.log('\n' + '='.repeat(80));
console.log('TESTE 2: Múltiplos sócios com diretor');
console.log('='.repeat(80));

let teste2 = [
  { nome: 'Maria Santos', cargo: 'Sócio', dataEntrada: '15/03/2018' },
  { nome: 'Pedro Oliveira', cargo: 'Diretor', dataEntrada: '01/01/2019' },
  { nome: 'Ana Costa', cargo: 'Sócio', dataEntrada: '10/07/2020' }
];

analisarSocioPrincipal(teste2, 'OLIVEIRA & SANTOS ALIMENTAÇÃO LTDA');

console.log('\n' + '='.repeat(80));
console.log('TESTE 3: Sócios com nomes similares à empresa');
console.log('='.repeat(80));

let teste3 = [
  { nome: 'Carlos Mendes', cargo: 'Sócio', dataEntrada: '01/06/2017' },
  { nome: 'Roberto Silva', cargo: 'Sócio', dataEntrada: '15/08/2018' },
  { nome: 'Antonio Pizzaria', cargo: 'Sócio Gerente', dataEntrada: '20/12/2019' }
];

analisarSocioPrincipal(teste3, 'ANTONIO PIZZARIA E RESTAURANTE LTDA');

console.log('\n' + '='.repeat(80));
console.log('TESTE 4: Caso complexo - presidente vs primeiro da lista');
console.log('='.repeat(80));

let teste4 = [
  { nome: 'Fernando Lima', cargo: 'Sócio Fundador', dataEntrada: '01/01/2015' },
  { nome: 'Mariana Costa', cargo: 'Presidente', dataEntrada: '01/03/2020' },
  { nome: 'Ricardo Santos', cargo: 'Sócio', dataEntrada: '01/05/2021' }
];

analisarSocioPrincipal(teste4, 'COSTA & LIMA GASTRONOMIA LTDA');

console.log('\n✅ Testes concluídos!');