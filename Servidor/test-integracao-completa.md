# Teste de Integração Completa - dadosig2

## Fluxo Atualizado

### 1. Content Script (Instagram)
- Extrai texto do perfil do Instagram
- Envia para o componente Angular via `postMessage`

### 2. Componente Angular (`novo-lead.component.ts`)
- Recebe texto via `processarDadosInstagram()`
- Chama `leadService.enviarDadosInstagram(textoInsta)`
- Recebe objeto Lead completo da API
- Preenche formulário com dados estruturados

### 3. API (`/dadosig2`)
- Recebe texto bruto
- Processa com ChatGPT
- Cria/encontra CrmEmpresa
- Retorna objeto Lead completo

### 4. Salvamento
- Usuário pode editar dados no formulário
- Ao salvar, usa dados do Lead processado como base
- Aplica modificações do formulário
- Salva via API padrão

## Estrutura de Dados

### Entrada (Content Script → Angular)
```javascript
{
  tipo: 'INSTAGRAM_DATA_RESPONSE',
  username: 'tori<PERSON>ushi_62',
  textoInsta: 'toriisushi_62\nTorii Sushi Goiânia\nRestaurante japonês...'
}
```

### Processamento (Angular → API dadosig2)
```javascript
{
  texto: 'toriisushi_62\nTorii Sushi Goiânia\nRestaurante japonês...',
  crmEmpresaId: null
}
```

### Resposta (API → Angular)
```javascript
{
  sucesso: true,
  dados: {
    // Objeto Lead completo
    crmEmpresaId: 123,
    nomeResponsavel: 'Torii Sushi Goiânia',
    empresa: 'Torii Sushi Goiânia',
    telefone: '62993057090',
    instagramHandle: 'toriisushi_62',
    instagramData: { /* dados estruturados */ },
    crmEmpresa: { /* objeto CrmEmpresa completo */ },
    // ... outros campos
  }
}
```

## Principais Mudanças no Componente

### 1. Nova Propriedade
```typescript
leadProcessadoAPI: any = null;
```

### 2. Função Atualizada
```typescript
preencherFormularioComLeadProcessado(leadProcessado: any)
```
- Agora recebe objeto Lead completo
- Simula `dadosInstagram` para compatibilidade com template
- Mapeia categoria para segmento automaticamente

### 3. Salvamento Inteligente
```typescript
salvarLead()
```
- Usa lead processado como base se disponível
- Aplica modificações do formulário
- Fallback para modo manual se necessário

## Benefícios

1. **Dados Mais Precisos**: IA extrai e estrutura dados automaticamente
2. **Menos Código**: Não precisa processar dados manualmente no frontend
3. **Empresa Automática**: CrmEmpresa criada automaticamente
4. **Flexibilidade**: Usuário pode editar dados antes de salvar
5. **Compatibilidade**: Mantém funcionamento com dados manuais

## Teste Manual

1. Abrir perfil do Instagram
2. Ativar extensão PromoKit
3. Verificar se dados são extraídos e processados
4. Confirmar preenchimento automático do formulário
5. Editar dados se necessário
6. Salvar lead

## Logs para Debug

- `console.log('Objeto Lead processado com sucesso:', leadProcessado)`
- `console.log('Lead processado e formulário preenchido:', this.lead)`
- `console.log('Dados simulados para template:', this.dadosInstagram)`
- `console.log('Salvando lead:', leadParaSalvar)`
