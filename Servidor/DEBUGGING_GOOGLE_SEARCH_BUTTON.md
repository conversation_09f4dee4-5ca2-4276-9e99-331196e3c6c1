# Debug Report: Google Search Button Not Appearing

## Problem Description
The Google search button controlled by `*ngIf="lead.empresa?.trim()"` is not appearing even when `lead.empresa` contains "Himitsu Sushi".

## Debug Implementation Added

### 1. Visual Debug Panel
A blue debug panel now appears at the top of the component showing:
- **lead.empresa value**: The exact string value and its length
- **lead.empresa type**: JavaScript type (should be "string")
- **lead.empresa?.trim()**: The trimmed value and its length
- **Boolean condition result**: The exact boolean result of the `*ngIf` condition
- **lead.cidade**: Current city value
- **currentStep**: Current wizard step
- **Timestamp**: When the data was last updated

### 2. Test Buttons
Three test buttons in the debug panel:
- **🧪 Set Test "Himitsu Sushi"**: Manually sets empresa to test value
- **🗑️ Clear empresa**: Clears the empresa field
- **🔄 Refresh Debug**: Updates the debug timestamp and logs

### 3. Visual Indicators
- **Green bordered box**: Shows when the actual Google search button appears
- **Red bordered box**: Always visible test button for CSS debugging
- **Inline styles with !important**: Override any CSS conflicts

### 4. Console Logging
Detailed console logs track when `lead.empresa` changes:
- `🐛 [ngOnInit]`: Component initialization
- `🐛 [preencherFormularioComDadosInstagram]`: When filled from Instagram data
- `🐛 [preencherFormularioComLeadProcessado]`: When filled from API data
- `🐛 [selecionarCnpj]`: When updated from CNPJ selection
- `🐛 DEBUG UPDATE`: Manual refresh logs

## What to Look For

### 1. Data Issues
Check the debug panel:
- Is `lead.empresa` actually set to "Himitsu Sushi"?
- Is the type "string"?
- Does trimming work correctly?
- Is the boolean condition `true`?

### 2. Change Detection Issues
- Do you see the console logs when empresa is set?
- Does the debug timestamp update?
- Does clicking "🔄 Refresh Debug" make the button appear?

### 3. CSS Issues
- Does the red "TEST BUTTON (Always Visible)" appear?
- If yes, but the green button doesn't, it's a template condition issue
- If no, there's a CSS problem hiding the entire `.wizard-actions-bar`

### 4. Timing Issues
- Does the button appear after clicking "🧪 Set Test"?
- Check console for the order of log messages

## Common Scenarios

### Scenario A: Data Type Issue
**Symptoms**: Debug shows `lead.empresa` but type is not "string"
**Cause**: Empresa is an object or null instead of string
**Solution**: Fix data assignment in the backend/API

### Scenario B: Hidden Characters
**Symptoms**: Debug shows empresa but boolean condition is false
**Cause**: String contains hidden characters (like zero-width spaces)
**Solution**: Check the actual string bytes in console

### Scenario C: Change Detection
**Symptoms**: Console logs don't appear when empresa should be set
**Cause**: Angular change detection not triggered
**Solution**: Check if `cdr.detectChanges()` calls are working

### Scenario D: CSS Conflicts
**Symptoms**: Red test button doesn't appear
**Cause**: CSS hiding the wizard-actions-bar
**Solution**: Check for parent element visibility or CSS conflicts

## Testing Steps

1. **Load the component** and check the debug panel immediately
2. **Look for console logs** during component initialization
3. **Test with manual button**: Click "🧪 Set Test" and see if green button appears
4. **Clear and test**: Click "🗑️ Clear" then set again
5. **Check browser dev tools**: Inspect the DOM for the wizard-actions-bar element

## Quick Fix Tests

If the issue persists, try these quick tests:

### Test 1: Simplify Condition
Change the template condition from:
```html
*ngIf="lead.empresa?.trim()"
```
to:
```html
*ngIf="lead.empresa"
```

### Test 2: Hardcode Test
Replace the condition with:
```html
*ngIf="true"
```

### Test 3: Alternative Property
Use a different property:
```html
*ngIf="lead.nomeResponsavel?.trim()"
```

## Remove Debug Code
After identifying and fixing the issue, remove:
1. The blue debug panel in the template
2. The red test button section
3. All `console.log` statements with 🐛 emoji
4. The debug methods in the TypeScript component
5. The `debugTimestamp` property

The debug code is clearly marked with comments for easy removal.