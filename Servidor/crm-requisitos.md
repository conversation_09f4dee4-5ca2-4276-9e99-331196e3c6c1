# 📋 Requisitos Funcionais - CRM CardápioTech

## 🎯 **Visão Geral**
Sistema de CRM especializado para o CardápioTech, focado em leads do segmento de alimentação (restaurantes, pizzarias, lanchonetes, etc.) com forte integração com redes sociais, especialmente Instagram e WhatsApp.

---

## 📱 **1. Visualização e Listagem de Leads**

### **Lista/Grid Principal**
- **Cards compactos** por lead exibindo:
  - Nome do responsável e empresa
  - Telefone/WhatsApp
  - Instagram handle (@usuario)
  - Etapa atual do funil
  - Score/qualificação visual (cores)
  - Data da última interação
  - Data do próximo follow-up
  - Origem do lead (Instagram, Site, Indicação, etc.)

### **Visualização**
- **Layout em grid** responsivo (2-4 colunas)
- **Paginação** ou scroll infinito
- **Ordenação** por: data criação, última interação, score, próximo follow-up
- **Contadores** por etapa do funil no topo

---

## 🔍 **2. Filtros e Busca Avançada**

### **Busca Textual**
- Busca por nome do responsável
- Busca por nome da empresa
- Busca por telefone
- Busca por Instagram handle

### **Filtros por Etapa**
- Prospecção
- Qualificação
- Objeção
- Fechamento
- Perdido/Inativo

### **Filtros por Qualificação**
- Score Alto (80-100%)
- Score Médio (50-79%)
- Score Baixo (0-49%)
- Sem qualificação

### **Filtros por Origem**
- Instagram (coleta automática)
- Site/Landing Page
- WhatsApp direto
- Indicação
- Evento/Feira
- Outros

### **Filtros por Tempo**
- Criados hoje
- Criados esta semana
- Criados este mês
- Follow-up vencido
- Follow-up hoje
- Follow-up esta semana

### **Filtros por Segmento**
- Restaurante
- Pizzaria
- Lanchonete
- Hamburgueria
- Confeitaria/Doceria
- Bar/Boteco
- Food Truck
- Outros

---

## 🚀 **3. Ações Rápidas por Lead**

### **Comunicação Direta**
- **Ligar** - abrir discador do sistema
- **WhatsApp** - abrir conversa com mensagem pré-definida
- **E-mail** - enviar e-mail template
- **Instagram** - abrir perfil do lead

### **Gestão do Funil**
- **Visualizar detalhes** - abrir tela CRM Home específica
- **Mover etapa** - avançar/retroceder no funil
- **Agendar follow-up** - definir próximo contato
- **Adicionar nota** - observação rápida

### **Ações em Lote**
- Selecionar múltiplos leads
- Mover etapa em massa
- Exportar selecionados
- Enviar e-mail em massa

---

## 📝 **4. Cadastro de Leads**

### **⭐ Cadastro via Instagram (PRIORITÁRIO)**
- **Input do Instagram handle** (@usuario ou URL do perfil)
- **Coleta automática de dados:**
  - Nome real (se disponível no bio)
  - Biografia completa
  - Número de seguidores
  - Número de seguindo
  - Tipo de conta (pessoal/business)
  - Categoria do negócio (se business)
  - Localização (se disponível)
  - Site/contato (se no bio)
  - Últimas fotos para análise do negócio

### **Geração de Rapport Automático**
- **Análise das últimas 9 fotos** do Instagram
- **Identificação do tipo de negócio** (pizzaria, restaurante, etc.)
- **Análise do cardápio** (se visível nas fotos)
- **Identificação de pontos de dor:**
  - Atendimento manual visível
  - Filas/aglomerações
  - Cardápio físico/papel
  - Falta de tecnologia
- **Geração de sugestões de abordagem** baseadas na análise
- **Pontos de interesse** para iniciar conversa

### **Cadastro Manual Tradicional**
- Formulário completo com todos os campos
- Upload de foto/avatar
- Campos obrigatórios vs. opcionais
- Validação de dados (telefone, e-mail, etc.)

### **Importação em Lote**
- Upload de CSV/Excel
- Mapeamento de colunas
- Validação e prévia antes da importação
- Log de erros/conflitos

---

## 📊 **5. Visualização Kanban/Pipeline**

### **Colunas do Funil**
- **Prospecção** - novos leads, primeiro contato
- **Qualificação** - interesse confirmado, necessidades identificadas
- **Objeção** - resistências identificadas, negociação
- **Fechamento** - proposta enviada, aguardando decisão
- **Ganho** - cliente fechado
- **Perdido** - lead desqualificado ou perdido

### **Funcionalidades Kanban**
- **Drag & drop** entre colunas
- **Contadores** de leads por coluna
- **Valor potencial** total por coluna
- **Cards compactos** com informações essenciais
- **Filtros** aplicáveis à visualização Kanban

---

## 📈 **6. Dashboard e Métricas**

### **Visão Geral**
- Total de leads ativos
- Leads criados hoje/semana/mês
- Taxa de conversão geral
- Follow-ups vencidos (alerta)
- Meta vs. realizado do mês

### **Métricas por Etapa**
- Tempo médio por etapa
- Taxa de conversão entre etapas
- Leads "presos" em cada etapa
- Principais motivos de perda

### **Métricas por Origem**
- Conversão por canal (Instagram vs. outros)
- Qualidade dos leads por origem
- ROI por canal de aquisição

### **Performance do Vendedor**
- Leads atribuídos
- Taxa de conversão pessoal
- Tempo de resposta médio
- Follow-ups em dia

---

## 🔧 **7. Gestão e Configuração**

### **Perfis de Lead**
- Edição completa de informações
- Histórico de interações
- Anexos/documentos
- Log de mudanças de etapa

### **Templates e Automação**
- Templates de mensagem por etapa
- Lembretes automáticos de follow-up
- Sequências de e-mail
- Webhooks para integrações

### **Configurações do Sistema**
- Etapas personalizáveis do funil
- Campos customizados
- Critérios de qualificação/score
- Regras de atribuição de leads

---

## 🌐 **8. Integrações Prioritárias**

### **Redes Sociais**
- **Instagram** - coleta de dados e análise
- **WhatsApp** - envio de mensagens
- **LinkedIn** - informações profissionais

### **Comunicação**
- **E-mail** - templates e disparo
- **SMS** - notificações e lembretes
- **Telefonia** - discador integrado

### **Ferramentas Externas**
- **Google Maps** - localização e direções
- **Google Analytics** - tracking de conversões
- **Zapier/Make** - integrações diversas

---

## 📱 **9. Responsividade e UX**

### **Design Responsivo**
- Otimizado para desktop (uso principal)
- Funcional em tablet
- Visualização básica em mobile

### **Performance**
- Carregamento rápido da lista
- Lazy loading de imagens
- Cache de dados frequentes
- Busca em tempo real

### **Usabilidade**
- Shortcuts de teclado
- Ações por clique direito
- Tooltips informativos
- Feedback visual para ações

---

## 🎯 **10. Prioridades de Desenvolvimento**

### **Fase 1 - MVP (Mínimo Viável)**
1. ✅ Tela CRM Home (já desenvolvida)
2. 🔄 Lista básica de leads
3. 🔄 Cadastro via Instagram com coleta automática
4. 🔄 Filtros essenciais (etapa, busca)
5. 🔄 Ações rápidas básicas

### **Fase 2 - Expansão**
6. Visualização Kanban
7. Dashboard com métricas
8. Templates de mensagem
9. Importação/exportação

### **Fase 3 - Avançado**
10. Relatórios avançados
11. Automações
12. Integrações externas
13. Mobile app

---

## 💡 **Diferenciais Competitivos**

1. **Especialização no segmento alimentação**
2. **Coleta automática via Instagram com IA**
3. **Geração de rapport baseado em análise visual**
4. **Integração nativa com WhatsApp**
5. **Processo otimizado para vendas de tecnologia para restaurantes**

---

*Documento criado em: ${new Date().toLocaleDateString('pt-BR')}*
*Versão: 1.0*