# Melhorias na Extração de Links Múltiplos - dadosig2

## Problema Resolvido
O sistema anteriormente extraía apenas dois campos de links únicos (`website` e `linkInsta`), limitando a capacidade de capturar todos os links importantes presentes nos perfis do Instagram.

## Solução Implementada

### 1. **Nova Estrutura de Dados**
Adicionado campo `links` que permite extrair múltiplos links categorizados:

```typescript
"links": [
  {
    "tipo": "Site|Ifood|WhatsApp|Instagram|Localização|SiteCardapio|Concorrente|Reservas",
    "url": "url_completa_válida", 
    "descricao": "descrição_clara_do_link",
    "ordem": numero_de_1_a_10
  }
]
```

### 2. **Categorização Inteligente de Links**
O sistema agora identifica e categoriza automaticamente:

- **Site**: Sites oficiais da empresa, blogs corporativos
- **Ifood**: Links do iFood, menções ao iFood
- **WhatsApp**: Links wa.me, números com contexto WhatsApp
- **Instagram**: Links de outros perfis Instagram relacionados
- **Localização**: Google Maps, endereços clicáveis, coordenadas
- **SiteCardapio**: Cardápios online próprios ou neutros
- **Concorrente**: Sistemas de cardápio de terceiros (Goomer, etc.)
- **Reservas**: Sistemas de reserva de mesa (OpenTable, etc.)

### 3. **Prompt de IA Melhorado**
Adicionada seção específica no prompt que instrui a IA a:

```
🔍 PROCURE e EXTRAIA TODOS os links encontrados no texto do perfil:
1. Links na bio/descrição
2. Links em stories highlights mencionados
3. Links mencionados em postagens
4. URLs completas ou parciais (como "meusite.com.br")
5. Menções a plataformas (iFood, WhatsApp, etc.)
6. Referências a redes sociais
7. Links de localização/mapas
```

### 4. **Regras de Extração Robustas**
Implementadas regras específicas para:
- Extrair URLs completas quando disponíveis
- Montar URLs para WhatsApp: `https://wa.me/55DDNUMERO`
- Montar URLs para Instagram: `https://instagram.com/username`
- Adicionar `https://` para sites incompletos
- Ordenar por relevância (1=mais importante)
- Usar descrições claras e úteis

### 5. **Processamento Backend Inteligente**
Adicionado código que:

```typescript
// Processar links múltiplos se fornecidos pela IA
if (dadosLead.links && Array.isArray(dadosLead.links)) {
  console.log('Processando links múltiplos:', dadosLead.links);
  
  // Converter para instâncias LeadLink
  const linksFormatados = dadosLead.links.map((link: any, index: number) => {
    // Validar e mapear tipos
    let tipoLink = link.tipo;
    if (tipoLink === 'SiteCardapio') tipoLink = TipoLinkLead.SiteCardapio;
    if (tipoLink === 'Localização') tipoLink = TipoLinkLead.Localizacao;
    if (tipoLink === 'WhatsApp') tipoLink = TipoLinkLead.Whatsapp;
    
    return new LeadLink(0, tipoLink, link.url, link.descricao, link.ordem || index + 1);
  });
  
  lead.links = linksFormatados;
}
```

### 6. **Compatibilidade Mantida**
Os campos `website` e `linkInsta` são mantidos para compatibilidade:
- `website`: Populado automaticamente com o primeiro link de tipo "Site"
- `linkInsta`: Populado com link de cardápio se diferente do website

### 7. **Validação no Checklist**
Expandido o checklist final da IA:

```
🔗 VALIDAÇÃO DE LINKS:
6. "Extraí TODOS os links mencionados no texto?"
7. "Categorizei cada link com o tipo correto?"
8. "Montei URLs completas quando necessário?"
9. "Ordenei links por relevância?"
10. "Se não encontrei links, deixei array vazio?"
```

## Exemplos de Uso

### Entrada (Texto do Instagram):
```
🍕 Pizza artesanal delivery
📱 WhatsApp: (11) 99999-9999
🛍️ Peça pelo iFood: minha-pizzaria
🌐 Site: www.minhapizzaria.com
📍 Rua das Flores, 123 - Centro
```

### Saída (Links Extraídos):
```json
[
  {
    "tipo": "WhatsApp",
    "url": "https://wa.me/5511999999999",
    "descricao": "WhatsApp para pedidos",
    "ordem": 1
  },
  {
    "tipo": "Ifood", 
    "url": "https://ifood.com.br/delivery/minha-pizzaria",
    "descricao": "Cardápio iFood",
    "ordem": 2
  },
  {
    "tipo": "Site",
    "url": "https://www.minhapizzaria.com", 
    "descricao": "Site oficial",
    "ordem": 3
  },
  {
    "tipo": "Localização",
    "url": "https://maps.google.com/?q=Rua+das+Flores+123+Centro",
    "descricao": "Ver localização",
    "ordem": 4
  }
]
```

## Benefícios Alcançados

1. **Extração Completa**: Captura todos os links presentes no perfil
2. **Categorização Automática**: Links organizados por tipo/finalidade
3. **Análise Aprimorada**: Mais dados para análise de concorrência
4. **Compatibilidade Total**: Código existente continua funcionando
5. **Escalabilidade**: Fácil adição de novos tipos de link
6. **URLs Completas**: Sistema constrói URLs válidas automaticamente

## Localização das Alterações

### Arquivo: `server/routes/leads.ts`
1. **Estrutura JSON** (linhas 867-874): Adicionado campo `links` array
2. **Prompt da IA** (linhas 879-942): Nova seção de extração de links múltiplos
3. **Checklist** (linhas 1102-1107): Validação específica para links
4. **Processamento** (linhas 1284-1338): Código para processar links múltiplos

## Como Testar

1. **Teste com múltiplos links**: Usar perfil com WhatsApp, iFood, site e localização
2. **Teste com links parciais**: Verificar se URLs incompletas são completadas
3. **Teste sem links**: Confirmar que retorna array vazio
4. **Teste de compatibilidade**: Verificar se `website` e `linkInsta` são populados

## Próximos Passos Possíveis

1. Adicionar validação de URLs no frontend
2. Criar interface para editar links extraídos
3. Implementar análise automática de concorrentes baseada nos links
4. Adicionar métricas de qualidade dos links encontrados

Com essas melhorias, o sistema agora extrai e organiza automaticamente todos os links importantes dos perfis do Instagram, fornecendo uma visão muito mais completa e útil para análise de leads.