-- Script para adicionar tabela de links dos leads do CRM
-- Per<PERSON> múltiplos links por lead (Ifood, Site, WhatsApp, etc.)

CREATE TABLE crm_lead_link (
  id bigint NOT NULL AUTO_INCREMENT,
  crm_lead_id bigint NOT NULL,
  tipo varchar(32) NOT NULL,
  url varchar(512) NOT NULL,
  descricao varchar(255) NULL,
  ativo bit(1) DEFAULT 1,
  ordem int DEFAULT 0,
  created_at datetime NOT NULL,
  updated_at datetime NOT NULL,
  PRIMARY KEY (id),
  INDEX idx_crm_lead_id (crm_lead_id),
  INDEX idx_tipo (tipo),
  INDEX idx_ativo (ativo),
  CONSTRAINT fk_crm_lead_link_lead FOREIGN KEY (crm_lead_id) REFERENCES crm_lead (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Migrar dados existentes do campo link_insta para a nova tabela
INSERT INTO crm_lead_link (crm_lead_id, tipo, url, descricao, ativo, ordem, created_at, updated_at)
SELECT 
  id,
  'Instagram',
  link_insta,
  'Link da bio do Instagram',
  1,
  0,
  NOW(),
  NOW()
FROM crm_lead 
WHERE link_insta IS NOT NULL AND link_insta != '';

-- Migrar dados existentes do campo site para a nova tabela
INSERT INTO crm_lead_link (crm_lead_id, tipo, url, descricao, ativo, ordem, created_at, updated_at)
SELECT 
  id,
  'Site',
  site,
  'Site da empresa',
  1,
  1,
  NOW(),
  NOW()
FROM crm_lead 
WHERE site IS NOT NULL AND site != '';
