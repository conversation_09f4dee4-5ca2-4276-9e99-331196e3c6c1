create table integracao_gateway_pagamento (
  id bigint NOT NULL AUTO_INCREMENT,
  gateway varchar(255) not NULL,
  public_key varchar(255) DEFAULT NULL,
  private_key varchar(255) DEFAULT NULL,
  instalacao_id  varchar(255) DEFAULT NULL ,
  loja_id  varchar(255) DEFAULT NULL,
  cliente_id  varchar(255) DEFAULT NULL,
  sandbox bit(1) default null,
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


create table empresa_integracao_gateway_pagamento (
  empresa_id  bigint not null,
  integracao_gateway_pagamento_id bigint not null,
  data_instalacao datetime not null,
  data_atualizacao datetime   null,
      PRIMARY KEY (empresa_id, integracao_gateway_pagamento_id),
        CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),
        CONSTRAINT   FOREIGN KEY (integracao_gateway_pagamento_id) REFERENCES integracao_gateway_pagamento(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

alter table contato add column codigo_pagarme varchar(255) null;

alter table trecho_de_prompt add column condicao text null;



create table ifood_user_code (
  id bigint NOT NULL AUTO_INCREMENT,
   user_code varchar(255) not null,
   authorization_code_verifier varchar(400) not null,
   verification_url varchar(100) not null,
   expires_in datetime not null,
     empresa_id  bigint not null,
         PRIMARY KEY (id),
           CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id)
);


alter table integracao_ifood add column codigo_autorizacao varchar(50) null;
alter table integracao_ifood add column verificador_codigo_autorizacao varchar(300) null;
alter table integracao_ifood add column nome_loja varchar(100) null;
alter table pedido add column id_loja_externa varchar(255) null;

alter table produto_template add column ocultar_produtos bit(1) null;

update produto_template join empresa on empresa.catalogo_id =  produto_template.catalogo_id
    set ocultar_produtos = true where empresa.id in (1108,1241,1274,1323,1701);


update forma_de_pagamento set nome = 'ifood-online',descricao = 'Ifood Online' where id in ( select t.id from ( select id from
 forma_de_pagamento where descricao like 'ifood online%' and forma_de_pagamento_pdv_id is not null group by empresa_id) t );


update forma_de_pagamento set removida  = true where descricao like 'Ifood Online -%' and forma_de_pagamento_pdv_id is not null;


alter table usuario add column codigo_pdv varchar(50) null;
alter table cardapio add column exibir_produtos_valor_zerado_mesa bit(1) null default 0;


alter table trecho_de_prompt_empresa add column texto LONGTEXT NULL;

alter table trecho_de_prompt ADD COLUMN exemplos_json json DEFAULT NULL;
alter table trecho_de_prompt_empresa ADD COLUMN exemplos_json json DEFAULT NULL;
alter table trecho_de_prompt_empresa ADD COLUMN intent varchar(100) null;
ALTER TABLE trecho_de_prompt_empresa ADD COLUMN tipo ENUM('texto', 'exemplos') DEFAULT 'texto';



alter table pedido modify column contato_id bigint null;

alter table integracao_ifood add column refresh_token longtext null;
alter table integracao_ifood add column desativado bit(1) null;


alter table pagamento_pedido add column codigo_autenticacao varchar(125) null;
alter table integracao_gateway_pagamento modify column public_key varchar(500) null;
alter table integracao_gateway_pagamento add column refresh_token longtext null;
alter table integracao_gateway_pagamento add column data_expiracao datetime null;



CREATE TABLE notificacao_mesa (
  id bigint NOT NULL AUTO_INCREMENT,
  origem varchar(255)  NOT NULL,
  comando varchar(255)  NOT NULL,
  operacao varchar(255)  NOT NULL,
  numero varchar(255)  DEFAULT NULL,
  horario datetime NOT NULL,
  horario_notificado datetime NOT NULL,
  dados longtext ,
  executada bit(1) DEFAULT NULL,
  erro varchar(255)  DEFAULT NULL,
  ignorar bit(1) DEFAULT NULL,
      empresa_id  bigint not null,
      comanda_id  bigint not null,
          PRIMARY KEY (id),
            CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),
            CONSTRAINT   FOREIGN KEY (comanda_id) REFERENCES comanda (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


alter table cancelamento_empresa modify column  operador_id bigint(20) null;


create table request_parceiro (
  id bigint NOT NULL AUTO_INCREMENT,
  sistema varchar(100)  NOT NULL,
  horario datetime NOT NULL,
  payload longtext not null ,
  retorno longtext   null ,
  header longtext   null ,
  httpstatus int not null,
    erro varchar(255) null,
  empresa_id  bigint not null,
  pedido_id  bigint not null,

     PRIMARY KEY (id),
                 CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),
                 CONSTRAINT   FOREIGN KEY (pedido_id) REFERENCES pedido (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


alter table campanha_rede add column  link_imagem varchar(255) NULL;

insert into modulo(id, nome) values (10, 'google api maps');
alter table empresa add column google_maps_key varchar(100) null;


alter table configuracoes_mia add column modelo varchar(50) not null default 'Basico';


alter table configuracoes_mia add column data_inicio_trial datetime null;


alter table notificacao_delivery add column event_id varchar(150) null;
alter table delivery_pedido add column endereco_confirmado bit(1) null;

create table pedido_alteracao_endereco (
  id varchar(255) not null,
  horario datetime NOT NULL,
  empresa_id  bigint not null,
  pedido_id  bigint not null,
  order_id varchar(255) not null,
  metadata longtext null,
  data_expiracao datetime not null,
  aceito bit(1) null,
  acao varchar(100) null,
  operador_id bigint null,
  horario_resposta datetime null,
       PRIMARY KEY (id),
                   CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),
                   CONSTRAINT   FOREIGN KEY (pedido_id) REFERENCES pedido (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

alter table integracao_ifood add column solicitar_automatico bit(1)   null ;
alter table integracao_ifood add column confirmar_cotacao bit(1)    null;


ALTER TABLE pedido_alteracao_endereco ADD INDEX idx_horarioexpiracao (data_expiracao);
ALTER TABLE pedido_disputa ADD INDEX idx_horarioexpiracao (data_expiracao);


alter table integracao_ifood add column order_api bit(1) default 1;
alter table integracao_ifood add column shipping_api bit(1) default 0;

alter table delivery_pedido add column operador_id bigint null;
alter table delivery_pedido modify column status varchar(50) NOT NULL;

alter table registro_de_login
  add column hostname varchar(255) null,
  add column city varchar(100) null,
  add column region varchar(100) null,
  add column country varchar(2) null,
  add column loc varchar(50) null,
  add column org varchar(255) null,
  add column postal varchar(20) null,
  add column timezone varchar(50) null;



CREATE TABLE tema_personalizado (
  id bigint PRIMARY KEY AUTO_INCREMENT,
  nome VARCHAR(50) NOT NULL,
  cor_fundo VARCHAR(50) NOT NULL,
  cor_texto_fundo VARCHAR(50) NOT NULL,
  cor_botao VARCHAR(50) NOT NULL,
  cor_texto_botao VARCHAR(50) NOT NULL,
  cor_texto_primaria VARCHAR(50) NOT NULL,
  cor_texto_secundaria VARCHAR(50) NOT NULL,

  empresa_id  bigint not null,
  CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id)
);

alter table empresa add column tema_personalizado_id bigint null;
alter table empresa add constraint tema_personalizado_id foreign key (tema_personalizado_id) references tema_personalizado(id);


alter table produto_template_tamanho add column  pontos_ganhos  int null;
alter table produto_template_tamanho add column  cashback  decimal(9,3) null;

alter table produto_tamanho add column  pontos_ganhos  int null;
alter table produto_tamanho add column  cashback  decimal(9,3) null;

alter table token add column maquina_id varchar(50) null;
alter table token add column validado bit(1) null;


alter table pagamento_pedido add column estorno_id varchar(100) null;
alter table pagamento_pedido add column url_autenticar varchar(255) null;

alter table pagamento_pedido add column tokenizacao_id varchar(150) null;
alter table config_meio_pagamento add column tokenizar bit(1) null;

ALTER TABLE pagamento_pedido ADD INDEX idx_tokenizacao (tokenizacao_id);



insert into operacao_do_sistema(nome,descricao)
values ('Adicionar pontos no programa de fidelidade', 'Permissão adicionar pontos no programa de fidelidade');

insert into operacao_do_sistema(nome,descricao)
values ('Trocar brindes no programa de fidelidade', 'Permissão trocar pontos por brindes no programa de fidelidade');

insert into papel_operacao (papel_id, operacao_id)
 select id,12 from papel where exists (select 1 from papel_operacao where papel_id = papel.id and operacao_id = 1);

insert into papel_operacao (papel_id, operacao_id)
 select id,13 from papel where exists (select 1 from papel_operacao where papel_id = papel.id and operacao_id = 1);


alter table empresa add column permitir_cupom_mesas bit(1) default null;
alter table cupom add column forma_entrega varchar(25) null;
update empresa set permitir_cupom_mesas = true where id in (1352,1271);


alter table pagamento_pedido add column cpf varchar(11) null;


create table tag_produto(
  id bigint NOT NULL AUTO_INCREMENT,
  nome varchar(50) not NULL,
  descricao varchar(500) DEFAULT NULL,
  grupo varchar(50) not null,
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

create table produto_tags (
  produto_id bigint NOT NULL,
  tag_produto_id bigint NOT NULL,
    CONSTRAINT   FOREIGN KEY (produto_id) REFERENCES produto (id),
      CONSTRAINT   FOREIGN KEY (tag_produto_id) REFERENCES tag_produto (id)
);


insert into tag_produto(nome,descricao,grupo)
values ('Vegetariano','Não contém carne ou subprodutos de animais', 'alimentar'),
       ('Vegano','Não contém produtos de origem animal, incluindo carne, peixe, frutos do mar, laticínios, ovos, mel e qualquer produto derivado de animais.', 'alimentar'),
       ('Orgânico','Sem agrotóxicos sintéticos, segundo a lei 10.831', 'alimentar'),
       ('Sem açúcar','Não contém nenhum tipo de açúcar (como sacarose, frutose, glicose, ou outros açúcares simples )', 'alimentar'),
       ('Zero lactose','Não contém lactose ( leite e devivados)', 'alimentar'),
       ('Sem gluten','Sem ingredientes que contenham trigo, cevada, centeio, espelta ou outros grãos que possuam glúten.', 'alimentar');


create table motivo_cancelamento_empresa(
  id bigint NOT NULL AUTO_INCREMENT,
  codigo varchar(50) not NULL,
  descricao varchar(255) not NULL,
  classificacao int not null,
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

create table cancelamento_empresa_motivo(
  cancelamento_empresa_id bigint NOT NULL,
  motivo_id bigint NOT NULL,
    CONSTRAINT   FOREIGN KEY (cancelamento_empresa_id) REFERENCES cancelamento_empresa (id),
      CONSTRAINT   FOREIGN KEY (motivo_id) REFERENCES motivo_cancelamento_empresa (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

insert into motivo_cancelamento_empresa(codigo,descricao,classificacao) values
      ('AUTOMATICO','Cancelamento automatico da empresa', 0),
      ('DIFICULDADE_NO_USO_OU_INTERFACE','Dificuldade no uso ou interface pouco intuitiva', 0),
      ('PROBLEMAS_TECNICOS_OU_FALHAS','Problemas técnicos frequentes ou mau funcionamento em momentos críticos', 0),
      ('SUPORTE_INEFICIENTE_OU_LIMITADO','Falta de suporte eficiente ou funcionalidades essenciais', 0),
      ('CONCORRENCIA','Concorrência com melhores ofertas ou custo elevado', 0),
      ('CUSTO','Custo elevado', 0),
      ('FALTA_DE_ATUALIZACOES_OU_INOVACAO','Falta de atualizações, evolução ou inovação', 0),
        ('FALTA_DE_FUNCAO_PDV','Falta funções de um PDV ( Gerenciar pagamento, estoque, nfe etc)', 0),
        ('CLIENTES_NAO_ADAPTARAM','Não houve adesão ao sistema por parte dos clientes', 0),
         ('EMPRESA_FECHOU','Falência ou fechamento da empresa', 0),
         ('SEM_RETORNO_FINANCEIRO','Investimento não trouxe retorno esperado', 0),
         ('PROBLEMA_COM_DELIVERY','Problemas com entrega própria (custo, problemas com devolução, etc)', 0)


alter table categoria add column removida bit(1) null;

alter table cupom add column restrito_aniversariantes bit(1) default null;


ALTER TABLE trecho_de_prompt ADD COLUMN posicao INT NOT NULL DEFAULT 0;
ALTER TABLE trecho_de_prompt_empresa ADD COLUMN posicao INT NOT NULL DEFAULT 0;


SET @pos := 0;
UPDATE trecho_de_prompt
SET posicao = (@pos := @pos + 1)
ORDER BY id ASC;

UPDATE trecho_de_prompt_empresa tpe
JOIN trecho_de_prompt tp ON tpe.trecho_de_prompt_id = tp.id
SET tpe.posicao = tp.posicao;

alter table contato add column entregador bit(1) null;
update contato set desativar_msg_mkt = true, entregador = true, tipo = null where tipo = 'Entregador';

alter table empresa add column enviar_links_botao bit(1) null;

alter table tema_personalizado add column cor_preco_adicional varchar(50) null;


alter table configuracoes_mia add column usar_fluxo_typebot bit(1) null;

alter table configuracoes_mia add column id_fluxo_typebot_whatsapp varchar(100) null;
alter table configuracoes_mia add column id_fluxo_typebot_instagram varchar(100) null;

alter table estado_chatbot add column id_sessao_typebot varchar(100) null;
alter table estado_chatbot add column id_fluxo_typebot varchar(100) null;
alter table estado_chatbot add column id_resultado_typebot varchar(100) null;


alter table estado_chatbot add column endereco longtext null;
alter table sessao_link_saudacao add column endereco longtext null;
alter table sessao_link_saudacao add column forma_de_entrega varchar(100) null;
alter table sessao_link_saudacao add column forma_de_pagamento varchar(100) null;

alter table estado_chatbot add column sessao_link_saudacao_id bigint null;

alter table estado_chatbot add constraint sessao_link_saudacao_id foreign key (sessao_link_saudacao_id) references sessao_link_saudacao(id);

<!-- add controle estoque -->
create table grupo_de_insumo(
  id bigint NOT NULL AUTO_INCREMENT,
  nome varchar(255) not NULL,
  empresa_id bigint NOT NULL,
    removido bit(1) null,
      PRIMARY KEY (id),
  CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



create table  estoque(
    id bigint NOT NULL AUTO_INCREMENT,
    quantidade decimal(9,3)  not NULL,
    quantidade_minima decimal(9,3)   NULL,
    quantidade_reservada  decimal(9,3)  default 0,
    ultima_atualizacao datetime   null,
    PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- add ficha tecnica
CREATE TABLE receita (
  id bigint PRIMARY KEY AUTO_INCREMENT,
  rendimento   DECIMAL(9, 3) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;



create table  insumo(
  id bigint NOT NULL AUTO_INCREMENT,
  nome varchar(255) not NULL,
  objeto  varchar(20) not null default 'insumo',
  codigo varchar(100) null,
  estoque_id bigint   NULL,
  unidade_medida_id bigint   NULL,
  grupo_de_insumo_id bigint   NULL,
  preco_de_custo decimal(9,3) null,
  total_vinculos int default 0,
  tipo  varchar(25) NULL,
  receita_id bigint null ,
  empresa_id bigint NOT NULL,
  removido bit(1) null,
  PRIMARY KEY (id),
  CONSTRAINT   FOREIGN KEY (estoque_id) REFERENCES estoque(id),
  CONSTRAINT   FOREIGN KEY (grupo_de_insumo_id) REFERENCES grupo_de_insumo(id),
  CONSTRAINT   FOREIGN KEY (grupo_de_insumo_id) REFERENCES grupo_de_insumo(id),
  CONSTRAINT   FOREIGN KEY (unidade_medida_id) REFERENCES unidade_medida(id),
  CONSTRAINT   FOREIGN KEY (receita_id) REFERENCES receita(id),
  CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


create table insumo_produto(
  insumo_id bigint NOT NULL,
  produto_id bigint NOT NULL,
  CONSTRAINT   FOREIGN KEY (insumo_id) REFERENCES insumo(id),
    CONSTRAINT   FOREIGN KEY (produto_id) REFERENCES produto(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

create table insumo_opcao(
  insumo_id bigint NOT NULL,
  opcao_id bigint NOT NULL,
  CONSTRAINT   FOREIGN KEY (insumo_id) REFERENCES insumo(id),
    CONSTRAINT   FOREIGN KEY (opcao_id) REFERENCES opcao_adicional_produto(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


create table motivo_da_movimentacao(
 id bigint PRIMARY KEY AUTO_INCREMENT,
 descricao  varchar(255) not null,
 tipo varchar(25) not null,
 automatica bit(1) null
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

create table movimentacao_estoque_insumo(
 id bigint NOT NULL AUTO_INCREMENT,
 tipo varchar(25) not null,
 quantidade decimal(9,3) not null,
 estoque_id bigint    NULL,
 insumo_id bigint  NULL,
 horario datetime not null,
 empresa_id bigint NOT NULL,
 pedido_id bigint   NULL,
 item_pedido_id bigint null,
 produto_id bigint null,
 operador_id bigint null,
 opcao_id bigint null,
 motivo_da_movimentacao_id bigint null,
 observacao varchar(255) null,
 unidade_medida_id  bigint null,
 movimentacao_estornada_id bigint null,
  status varchar(50) default 'Confirmada',
 PRIMARY KEY (id),
   unique(movimentacao_estornada_id),
   CONSTRAINT   FOREIGN KEY (insumo_id) REFERENCES insumo(id),
   CONSTRAINT   FOREIGN KEY (pedido_id) REFERENCES pedido(id),
   CONSTRAINT   FOREIGN KEY (item_pedido_id) REFERENCES item_pedido(id),
   CONSTRAINT   FOREIGN KEY (produto_id) REFERENCES produto(id),
   CONSTRAINT   FOREIGN KEY (opcao_id) REFERENCES opcao_adicional_produto(id),
   CONSTRAINT   FOREIGN KEY (motivo_da_movimentacao_id) REFERENCES motivo_da_movimentacao(id),
   CONSTRAINT   FOREIGN KEY (unidade_medida_id) REFERENCES unidade_medida(id),
   CONSTRAINT   FOREIGN KEY (movimentacao_estornada_id) REFERENCES movimentacao_estoque_insumo(id),
   CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


CREATE TABLE ingrediente_da_receita (
  id bigint PRIMARY KEY AUTO_INCREMENT,
  receita_id bigint NOT NULL,
  insumo_id bigint NOT NULL,
  unidade_medida_id bigint   not NULL,
  custo_medio DECIMAL(10, 2) null,
  custo_na_porcao DECIMAL(10, 2) null,
  quantidade DECIMAL(10, 3) NOT NULL,
  removido bit(1) null,
  FOREIGN KEY (receita_id) REFERENCES receita(id),
  FOREIGN KEY (insumo_id) REFERENCES insumo(id),
  FOREIGN KEY (unidade_medida_id) REFERENCES unidade_medida(id)
);

CREATE TABLE vinculo_ingrediente_produto (
  id bigint PRIMARY KEY AUTO_INCREMENT,
  produto_id bigint   NULL,
  opcao_id bigint   NULL,
  quantidade_consumida DECIMAL(10, 2) NOT NULL,
  FOREIGN KEY (produto_id) REFERENCES produto(id),
      CONSTRAINT   FOREIGN KEY (opcao_id) REFERENCES opcao_adicional_produto(id)
);


alter table produto add column   estoque_id bigint   NULL;
ALTER TABLE produto ADD FOREIGN KEY (estoque_id) REFERENCES estoque(id);
alter table empresa add column estoque_vinculado_produto bit(1) default null;

insert into modulo(id, nome) values (11, 'controle de estoque');

insert into unidade_medida(nome, sigla, valor_inicial_padrao, incremento_padrao)
    values ('Unidade', 'und', 1,1), ('Litro', 'l', 1,1) ;

insert into motivo_da_movimentacao(descricao, tipo, automatica)
  values ('Estoque atual','entrada', true),  ('Saída por benefeciamento','entrada', true),  ('Entrada por benefeciamento','entrada', null),
         ('Entrada manual','entrada', null),   ('Saida Manual','saida', null),
         ('Entrada por ajuste','entrada', null), ('Saida por ajuste','saida', null), ('Perda','saida', null),
         ('Entrada por pedido cancelado','entrada', true),  ('Saida por venda','saida', true);

alter table movimentacao_estoque_insumo add column retornar_estoque bit(1) default true;

alter table catalogo_da_rede add column removido bit(1) not null default false;

alter table trecho_de_prompt add column  ativo bit(1) default true;




alter table configuracoes_mia add column typebot_configurado bit(1) not null default false;
alter table configuracoes_mia add column workspace_id varchar(100) null default null;
alter table configuracoes_mia add column chave_api_typebot varchar(100) null default null;


alter table configuracoes_mia add column public_id_fluxo_whatsapp varchar(100) null;
alter table configuracoes_mia add column public_id_fluxo_instagram varchar(100) null;



<!-- resgatar brinde via pedidos loja online
alter table integracao_pedido_fidelidade add column resgatar_brinde bit(1) null;
alter table produto add column valor_resgate decimal(9,2) null;
alter table item_pedido add column valor_resgatado decimal (9,2) default 0;

alter table brinde_resgatado add column produto_id bigint  null;
alter table brinde_resgatado add constraint idxproduto foreign key (produto_id) references produto(id);
alter table brinde_resgatado modify column id_brinde bigint null;

insert into forma_de_pagamento_pdv(nome,tipo,metodo,opendelivery_method) values ('Resgate', 'FIDELIDADE', 'OUTRO','OTHER');
insert into forma_de_pagamento_pdv_forma_integrada(forma_de_pagamento_pdv_id,forma_de_pagamento_integrada_id) values
 (18,9), (18,70), (18,84), (18,78);

update forma_de_pagamento_pdv SET tipo = 'FIDELIDADE' WHERE  tipo = 'CASHBACK';
update forma_de_pagamento set forma_de_pagamento_pdv_id = 15 where nome  = 'cashback';
insert into forma_de_pagamento_pdv_forma_integrada(forma_de_pagamento_pdv_id,forma_de_pagamento_integrada_id) values
  (15,70), (15,84), (15,78);

alter table config_impressao add column ocultar_numero_cliente bit(1) null default false;
alter table config_impressao add column ocultar_cobranca bit(1) null default false;

alter table adicional_produto add column oculto bit(1) null;
alter table opcao_adicional_produto add column oculta bit(1) null;

alter table notificacao_ifood add column  merchant_id varchar(150) null;

alter table produto add column cupom_id bigint null;
alter table produto add constraint cupomidx foreign key (cupom_id) references cupom(id);
alter table cupom add column brinde_resgate bit(1) null;



CREATE TABLE cupom_forma_de_pagamento (
  cupom_id bigint   not null,
  forma_de_pagamento_id bigint   not null,

  FOREIGN KEY (cupom_id) REFERENCES cupom(id),
      CONSTRAINT   FOREIGN KEY (forma_de_pagamento_id) REFERENCES forma_de_pagamento(id)
);

CREATE TABLE cartao_cliente (
  id bigint NOT NULL AUTO_INCREMENT,
  codigo varchar(100) NOT NULL,
  ativo bit(1) NOT NULL DEFAULT 1,
  data_criacao datetime NOT NULL,
  empresa_id bigint NOT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY uk_codigo_empresa (codigo, empresa_id),
  CONSTRAINT fk_cartao_cliente_empresa FOREIGN KEY (empresa_id) REFERENCES empresa (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


alter table integracao_pedido_fidelidade add column ocultar_pontos bit(1) null;

CREATE TABLE contrato_meucardapio_pay (
   id bigint NOT NULL AUTO_INCREMENT,
   forma_de_pagamento varchar(50) not null,
   id_externo bigint not null,
   descricao varchar(255) not null,
   taxa_pix decimal(9,2) not null,
   taxa_cartao decimal(9,2) not null,
   padrao bit(1) default 0,
   producao bit(1) default 0,
     unique(id_externo),
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE meucardapio_pay (
  id bigint NOT NULL AUTO_INCREMENT,
  data_ativacao datetime   NULL,
  pix_liberado bit(1) DEFAULT NULL,
  cartao_credito_liberado bit(1) DEFAULT NULL,
  id_loja varchar(255)   NULL,
  loja_ativou bit(1) default null,
  merchant_id varchar(255)   NULL,
  empresa_id bigint NOT NULL,
  contrato_meucardapio_pay_id  bigint NOT NULL,
  ativacao_status  varchar(50) null,
  ativacao_erro varchar(255) null,
  pix_taxa_minima  decimal(9,2) not null,
  PRIMARY KEY (id),
  CONSTRAINT FOREIGN KEY (empresa_id) REFERENCES empresa (id),
  CONSTRAINT FOREIGN KEY (contrato_meucardapio_pay_id) REFERENCES contrato_meucardapio_pay (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

insert into contrato_meucardapio_pay(forma_de_pagamento,id_externo,descricao,taxa_pix,taxa_cartao,padrao,producao)
 values ('Pix',5118, 'Tuna Teste - 5% CC - 1% Pix',0.01, 0.05, true,false);

alter table empresa add column cadastro_pagamento_online bit(1) null;

update empresa set cadastro_pagamento_online = true where exists (select 1 from forma_de_pagamento where empresa_id = empresa.id and online is true );

insert into notificacao(ativada, empresa_id, mensagem, tipo_de_notificacao, pode_desativar, qtde_dias_nova_notificacao)
select 1,id,'Olá, [NomeContato], segue o link para pagamento do seu pedido: [Link_Cardapio]
', 'Link Pagamento do Pedido',0,-1 from empresa;

alter table sessao_link_saudacao add column pedido_guid varchar(255) null;

alter table pagamento_pedido add column valor_taxa_split decimal(9,2) null;

alter table empresa add column usar_cartao_cliente bit(1) null;

alter table empresa add column permitir_multiplas_comandas_mesa bit(1) default null;

ALTER TABLE forma_de_pagamento DROP INDEX empresa_id_2;

alter table empresa add column garcom_fechar_comandas bit(1) default 1;


-- Adiciona novas colunas de cores à tabela tema_personalizado
ALTER TABLE tema_personalizado ADD COLUMN cor_fundo_elementos VARCHAR(50);
ALTER TABLE tema_personalizado ADD COLUMN cor_borda VARCHAR(50);
ALTER TABLE tema_personalizado ADD COLUMN cor_sombra VARCHAR(50);
ALTER TABLE tema_personalizado ADD COLUMN cor_preco VARCHAR(50);
ALTER TABLE tema_personalizado ADD COLUMN cor_destaque VARCHAR(50);
ALTER TABLE tema_personalizado ADD COLUMN cor_hover VARCHAR(50);

ALTER TABLE tema_personalizado ADD COLUMN cor_texto_rodape VARCHAR(50);
ALTER TABLE tema_personalizado ADD COLUMN cor_fundo_rodape VARCHAR(50);
ALTER TABLE tema_personalizado ADD COLUMN cor_borda_rodape VARCHAR(50);

-- Adicionar coluna cor_texto_topo se não existir
ALTER TABLE tema_personalizado ADD COLUMN IF NOT EXISTS cor_texto_topo VARCHAR(255) DEFAULT '#000000' AFTER cor_hover;

UPDATE tema_personalizado SET
  cor_fundo_elementos = '#ffffff',
  cor_borda = '#e9e9e9',
  cor_sombra = 'rgba(0,0,0,0.1)',
  cor_preco = '#28a745',
  cor_destaque = '#ffc107',
  cor_hover = '#e9ecef'
WHERE cor_fundo_elementos IS NULL;

alter table tema_personalizado add column cor_fundo_site varchar(50) null;
alter table tema_personalizado add column cor_texto_topo varchar(50) null;

update tema_personalizado set cor_fundo_site = cor_fundo where cor_fundo_site is null;
update tema_personalizado set cor_texto_topo = cor_texto_secundaria where cor_texto_topo is null;

alter table raio_de_cobranca add column permite_frete_gratis bit(1) default 1;

alter table pagamento_pedido add column provedor_externo varchar(50) null;


UPDATE tema_personalizado SET
  cor_fundo_elementos = '#ffffff',
  cor_borda = '#e9e9e9',
  cor_sombra = 'rgba(0,0,0,0.1)',
  cor_preco = '#28a745',
  cor_destaque = '#ffc107',
  cor_hover = '#e9ecef'
WHERE cor_fundo_elementos IS NULL;


alter table zona_de_entrega add column desativada bit(1) null;

alter table tema_personalizado add column cor_item_ativo_rodape varchar(50) null;


/* Tabela para atendimentos de suporte técnico e customer success */
CREATE TABLE atendimento (
  id varchar(36) NOT NULL,
  grupo_id varchar(255) NOT NULL,
  empresa_id bigint NOT NULL,
  aberto_em datetime NOT NULL,
  fechado_em datetime DEFAULT NULL,
  status ENUM('aberto', 'resolvido') NOT NULL DEFAULT 'aberto',
  PRIMARY KEY (id),
  CONSTRAINT FOREIGN KEY (empresa_id) REFERENCES empresa (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


update tema_personalizado set cor_texto_preco_adicional = cor_texto_primaria;

alter table usuario add column removido bit(1) null default 0;
alter table usuario add column superadmin bit(1) null default 0;

-- Tornar usuários com IDs 1, 2 e 8 superadmin
update usuario set superadmin = 1 where id in (1, 2, 8);


CREATE TABLE cep_customizado (
  id bigint NOT NULL AUTO_INCREMENT,
  cep varchar(20) NOT NULL,
  logradouro varchar(255) NOT NULL,
  bairro varchar(255) NOT NULL,
  cidade varchar(255) NOT NULL,
  estado varchar(2) NOT NULL,
  complemento varchar(255) DEFAULT NULL,
  criado_em datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  atualizado_em datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Adicionando coluna para rastrear origem do cálculo da taxa de entrega
ALTER TABLE taxa_de_entrega_calculada ADD COLUMN calculado_por VARCHAR(50) NULL;

--expiraçao pagamentos pix , mensagens pagamento pendente e confirmado
alter table pagamento_pedido add column data_expiracao datetime null;

CREATE TABLE tarefa_mensagem_pagamento_pendente (
  id bigint NOT NULL AUTO_INCREMENT,
  pagamento_id bigint NOT NULL,
  contato_id bigint NOT NULL,
  guid_pedido varchar(255) not null,
  empresa_id bigint NOT NULL,
  horario_criacao datetime NOT NULL,
  horario_vencimento datetime NOT NULL,
  executada bit(1) DEFAULT 0,
  PRIMARY KEY (id),
  CONSTRAINT   FOREIGN KEY (empresa_id) REFERENCES empresa (id),
  CONSTRAINT   FOREIGN KEY (pagamento_id) REFERENCES pagamento_pedido (id),
  CONSTRAINT   FOREIGN KEY (contato_id) REFERENCES contato(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

create index idxvencimento on tarefa_mensagem_pagamento_pendente(horario_vencimento);

insert into notificacao(ativada, empresa_id, mensagem, tipo_de_notificacao, pode_desativar, qtde_dias_nova_notificacao,encurtar_links)
select 1,id,'Olá, [NomeContato]
*❗Notamos que seu pagamento para o pedido [Codigo_Pedido] ainda não foi confirmado.*

Seu pedido só poderá ser preparado após o pagamento do Pix. Caso precise de ajuda, estamos à disposição!

*Link para pagamento*: [Link_Cardapio]', 'Pagamento Pendente Online',0,-1,false from empresa;



insert into notificacao(ativada, empresa_id, mensagem, tipo_de_notificacao, pode_desativar, qtde_dias_nova_notificacao,encurtar_links)
select 1,id,'✅ Seu pagamento foi aprovado e seu pedido *[Codigo_Pedido]* está confirmado ✨', 'Pagamento Confirmado Online',0,-1,false from empresa;

alter table pedido add column identificacao_balcao varchar(255) null;

alter table catalogo add column loja_ifood varchar(255) null;


alter table pausa_programada modify column data_fim datetime null;
alter table pausa_programada add column cancelada bit(1) null;
alter table pausa_programada add column data_cancelamento datetime null;
alter table pausa_programada add column operador_cadastrou_id bigint null;
alter table pausa_programada add column operador_cancelou_id bigint null;

alter table alcance add column permite_frete_gratis bit(1) default 0;

alter table telefone_cloud_whatsapp modify column access_token_usuario_de_sistema varchar(500) null;

alter table comanda add column cartao_cliente_id bigint(20) null;
alter table comanda add FOREIGN KEY (cartao_cliente_id) REFERENCES cartao_cliente(id);
alter table empresa add column associar_cartao_fechamento_pedido bit(1) null;



CREATE TABLE cupom_categoria (
  id bigint NOT NULL AUTO_INCREMENT,
  cupom_id bigint NOT NULL,
  categoria_id bigint NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fk_cupom_categoria_cupom FOREIGN KEY (cupom_id) REFERENCES cupom (id),
  CONSTRAINT fk_cupom_categoria_categoria FOREIGN KEY (categoria_id) REFERENCES categoria (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


CREATE TABLE cupom_produto_template_tamanho (
  id bigint NOT NULL AUTO_INCREMENT,
  cupom_id bigint NOT NULL,
  produto_template_tamanho_id bigint NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fk_cupom_tamanho_cupom FOREIGN KEY (cupom_id) REFERENCES cupom (id),
  CONSTRAINT fk_cupom_tamanho_tamanho FOREIGN KEY (produto_template_tamanho_id) REFERENCES produto_template_tamanho (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE cupom_produto (
  id bigint NOT NULL AUTO_INCREMENT,
  cupom_id bigint NOT NULL,
  produto_id bigint NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fk_cupom_produto_cupom FOREIGN KEY (cupom_id) REFERENCES cupom (id),
  CONSTRAINT fk_cupom_produto_produto FOREIGN KEY (produto_id) REFERENCES produto (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

insert into cupom_categoria(cupom_id,categoria_id)
     select id,categoria_id from cupom where categoria_id is not null;

insert into cupom_produto_template_tamanho(cupom_id,produto_template_tamanho_id)
  select id,produto_template_tamanho_id from cupom where produto_template_tamanho_id is not null;


insert into cupom_produto(cupom_id, produto_id)
  select id,produto_id from cupom where produto_id is not null;


alter table mensagem_enviada add column qtde_tentativas int(11) default 0;
update mensagem_enviada set qtde_tentativas = 1;

-- Tabelas para Chatbot Instagram
CREATE TABLE resposta_chatbot_instagram (
  id bigint NOT NULL AUTO_INCREMENT,
  chave_resposta varchar(255) NOT NULL,
  titulo varchar(255) NOT NULL,
  mensagem text NOT NULL,
  icone varchar(255) DEFAULT NULL,
  ativa bit(1) DEFAULT 1,
  ordem int DEFAULT 0,
  empresa_id bigint NOT NULL,
  dados_instagram_id bigint DEFAULT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY uk_chave_empresa (chave_resposta, empresa_id),
  CONSTRAINT fk_resposta_chatbot_instagram_empresa FOREIGN KEY (empresa_id) REFERENCES empresa (id),
  CONSTRAINT fk_resposta_chatbot_instagram_dados FOREIGN KEY (dados_instagram_id) REFERENCES dados_instagram (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE opcao_resposta_chatbot_instagram (
  id bigint NOT NULL AUTO_INCREMENT,
  texto varchar(255) NOT NULL,
  tipo varchar(50) NOT NULL,
  destino varchar(255) DEFAULT NULL,
  url varchar(500) DEFAULT NULL,
  icone varchar(255) DEFAULT NULL,
  ordem int DEFAULT 0,
  ativo bit(1) DEFAULT 1,
  empresa_id bigint NOT NULL,
  resposta_chatbot_instagram_id bigint NOT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fk_opcao_resposta_chatbot_empresa FOREIGN KEY (empresa_id) REFERENCES empresa (id),
  CONSTRAINT fk_opcao_resposta_chatbot_resposta FOREIGN KEY (resposta_chatbot_instagram_id) REFERENCES resposta_chatbot_instagram (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Índices para melhor performance
CREATE INDEX idx_resposta_chatbot_empresa ON resposta_chatbot_instagram (empresa_id);
CREATE INDEX idx_resposta_chatbot_chave ON resposta_chatbot_instagram (chave_resposta);
CREATE INDEX idx_opcao_resposta_empresa ON opcao_resposta_chatbot_instagram (empresa_id);
CREATE INDEX idx_opcao_resposta_resposta ON opcao_resposta_chatbot_instagram (resposta_chatbot_instagram_id);


alter table catalogo add column ultima_atualizacao_produtos datetime null;
update  catalogo set ultima_atualizacao_produtos = atualizacao;


CREATE TABLE tablet (
  id bigint NOT NULL AUTO_INCREMENT,
  numero varchar(50) NOT NULL UNIQUE,
  pin_admin varchar(10)   NULL,
  mesa_id bigint DEFAULT NULL,
  empresa_id bigint NOT NULL,
  ativo bit(1) DEFAULT 1,
  data_ultimo_uso datetime DEFAULT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY tablet_numero_unique (numero),
  CONSTRAINT tablet_mesa_fk FOREIGN KEY (mesa_id) REFERENCES mesa (id),
  CONSTRAINT tablet_empresa_fk FOREIGN KEY (empresa_id) REFERENCES empresa (id),
  INDEX idx_tablet_numero (numero),
  INDEX idx_tablet_empresa (empresa_id),
  INDEX idx_tablet_ativo (ativo)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


insert into   tablet(numero,pin_admin,empresa_id,ativo) values ('TAB-1GCDH3','0000',45,1);

alter table produto_vendido add column item_pedido_id bigint null;

  -- Tabela de Empresas do CRM (independente da tabela empresa principal)
  CREATE TABLE crm_empresa (
    id bigint NOT NULL AUTO_INCREMENT,
    nome varchar(255) NOT NULL,
    cnpj varchar(18) NULL,
    telefone varchar(30) NULL,
    email varchar(255) NULL,
    endereco text NULL,
    ativa bit(1) DEFAULT 1,
    created_at datetime NOT NULL,
    updated_at datetime NOT NULL,
    PRIMARY KEY (id),
    UNIQUE KEY uk_cnpj (cnpj),
    INDEX idx_nome (nome),
    INDEX idx_ativa (ativa)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

  -- Tabela para Leads do CRM MeuCardapio
  CREATE TABLE crm_lead (
    id bigint NOT NULL AUTO_INCREMENT,
    crm_empresa_id bigint NOT NULL,
    nome_responsavel varchar(255) not NULL,
    telefone varchar(30) not NULL,
    endereco VARCHAR(500) DEFAULT NULL COMMENT 'Endereço completo da empresa',
    etapa varchar(32) not NULL,
    origem varchar(32) not NULL,
    data_criacao datetime not NULL,
    data_ultima_interacao datetime NULL,
    data_proximo_followup datetime NULL,
    score int NULL,
    segmento varchar(32) NULL,
    valor_potencial decimal(12,2) NULL,
    vendedor_id bigint NULL,
    motivo_perda varchar(255) NULL,
    data_fechamento datetime NULL,
    instagram varchar(255) NULL,
    bio_insta text NULL,
    seguidores int NULL,
    seguindo int NULL,
    tipo_conta_instagram varchar(16) NULL,
    categoria_instagram varchar(64) NULL,
    localizacao varchar(255) NULL,
    site text NULL,
    avatar_url varchar(512) NULL,
    tipo_negocio_detectado varchar(64) NULL,
    pontos_dor text NULL,
    link_insta text NULL,
    relatorio_ia_json longtext NULL,
    notas TEXT NULL COMMENT 'Observações gerais e dados extraídos do Instagram',
    observacoes TEXT NULL COMMENT 'Observações específicas sobre vendas e negociação',
    created_at datetime not NULL,
    updated_at datetime not NULL,
    PRIMARY KEY (id),
    INDEX idx_crm_empresa (crm_empresa_id),
    INDEX idx_vendedor (vendedor_id),
    CONSTRAINT fk_crm_lead_empresa FOREIGN KEY (crm_empresa_id) REFERENCES crm_empresa (id)
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Inserir empresa CRM padrão para teste
  INSERT INTO crm_empresa (nome, cnpj, telefone, email, endereco, ativa, created_at, updated_at)
  VALUES ('MeuCardápio - CRM Padrão', '00.000.000/0001-00', '(11) 99999-9999', '<EMAIL>', 'São Paulo, SP', 1, NOW(), NOW());


alter table estoque add column ativo bit(1) default true;

-- Tabela para múltiplos links dos leads do CRM
CREATE TABLE crm_lead_link (
  id bigint NOT NULL AUTO_INCREMENT,
  crm_lead_id bigint NOT NULL,
  tipo varchar(32) NOT NULL,
  url varchar(512) NOT NULL,
  descricao varchar(255) NULL,
  ativo bit(1) DEFAULT 1,
  ordem int DEFAULT 0,
  created_at datetime NOT NULL,
  updated_at datetime NOT NULL,
  PRIMARY KEY (id),
  INDEX idx_crm_lead_id (crm_lead_id),
  INDEX idx_tipo (tipo),
  INDEX idx_ativo (ativo),
  CONSTRAINT fk_crm_lead_link_lead FOREIGN KEY (crm_lead_id) REFERENCES crm_lead (id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

