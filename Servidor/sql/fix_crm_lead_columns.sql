-- Script para adicionar colunas faltantes na tabela crm_lead
-- Problema: Mapeador Lead.xml espera colunas que não existem no banco

-- Adicionar coluna empresa (usada nos filtros de busca)
ALTER TABLE crm_lead ADD COLUMN empresa varchar(255) NULL;

-- Adicionar colunas relacionadas ao Instagram
ALTER TABLE crm_lead ADD COLUMN bio_insta text NULL;
ALTER TABLE crm_lead ADD COLUMN link_insta varchar(255) NULL;
ALTER TABLE crm_lead ADD COLUMN categoria_instagram varchar(64) NULL;
ALTER TABLE crm_lead ADD COLUMN localizacao varchar(255) NULL;
ALTER TABLE crm_lead ADD COLUMN site varchar(255) NULL;
ALTER TABLE crm_lead ADD COLUMN avatar_url varchar(512) NULL;
ALTER TABLE crm_lead ADD COLUMN fotos_json longtext NULL;

-- Adicionar colunas relacionadas à IA/Rapport
ALTER TABLE crm_lead ADD COLUMN tipo_negocio_detectado varchar(64) NULL;
ALTER TABLE crm_lead ADD COLUMN pontos_dor text NULL;
ALTER TABLE crm_lead ADD COLUMN sugestao_abordagem text NULL;
ALTER TABLE crm_lead ADD COLUMN relatorio_ia_json longtext NULL;

-- Verificar estrutura da tabela após as alterações
-- SHOW COLUMNS FROM crm_lead;