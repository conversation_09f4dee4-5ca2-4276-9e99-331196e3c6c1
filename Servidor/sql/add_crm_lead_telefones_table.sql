-- <PERSON><PERSON><PERSON> da tabela para múltiplos telefones dos leads
CREATE TABLE IF NOT EXISTS crm_lead_telefone (
  id int(11) NOT NULL AUTO_INCREMENT,
  crm_lead_id int(11) NOT NULL,
  tipo varchar(50) NOT NULL,
  numero varchar(20) NOT NULL,
  descricao varchar(255) DEFAULT NULL,
  ativo tinyint(1) NOT NULL DEFAULT 1,
  ordem int(11) NOT NULL DEFAULT 0,
  created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  KEY idx_crm_lead_telefone_lead_id (crm_lead_id),
  KEY idx_crm_lead_telefone_tipo (tipo),
  KEY idx_crm_lead_telefone_ativo (ativo),
  CONSTRAINT fk_crm_lead_telefone_lead FOREIGN KEY (crm_lead_id) REFERENCES crm_lead (id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Índice composto para busca eficiente por lead e tipo
CREATE INDEX idx_crm_lead_telefone_lead_tipo ON crm_lead_telefone (crm_lead_id, tipo);

-- Índice para ordenação
CREATE INDEX idx_crm_lead_telefone_ordem ON crm_lead_telefone (crm_lead_id, ordem, created_at);