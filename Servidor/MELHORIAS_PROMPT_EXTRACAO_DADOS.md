# Melhorias no Prompt de Extração de Dados do Instagram

## Problema Identificado
A IA estava inventando telefones que não pertenciam às empresas durante a extração de dados do Instagram na rota `/dadosig2`.

## Soluções Implementadas

### 1. **Regras Críticas Anti-Invenção**
- ⚠️ Adicionada seção "NUNCA INVENTE DADOS" no início do prompt
- 🚫 Proibição absoluta de inventar, deduzir ou gerar qualquer informação
- ✅ Regra de ouro: apenas extrair dados LITERALMENTE presentes no texto
- ✅ Instrução clara: "SE NÃO TIVER CERTEZA → USE NULL"

### 2. **Regras Específicas para Telefones Ultra-Conservadoras**
```
🔍 PROCURE apenas por telefones com indicadores INEQUÍVOCOS:
✅ Emojis de telefone: ☎️ 📞 📱 📲
✅ Palavras-chave: "Tel:", "Telefone:", "WhatsApp:", "Contato:", "Fone:"
✅ Formato telefônico claro: (XX) XXXX-XXXX ou (XX) 9XXXX-XXXX

🚫 NUNCA considere como telefone:
❌ Horários: "11:30", "18h", "das 9h às 22h"
❌ Números de endereço: "Rua 123", "número 300"
❌ Anos: "2023", "desde 2020"
❌ Quantidades: "há 5 anos", "mais de 100"
❌ Códigos: "CEP", códigos de produto
❌ Números isolados sem contexto de contato
```

### 3. **Exemplos Práticos de Validação**
Adicionados exemplos claros do que NÃO é telefone:
- ❌ "Aberto das 11:30 às 22:00" → São horários, NÃO telefones
- ❌ "Rua das Flores, 123" → É número de endereço, NÃO telefone
- ❌ "Desde 2020" → É ano, NÃO telefone
- ❌ "Delivery até as 23h" → É horário, NÃO telefone

E exemplos do que SÃO telefones válidos:
- ✅ "☎️ (62) 3333-4444" → TEM emoji de telefone
- ✅ "📱 WhatsApp: 99999-8888" → TEM emoji e palavra-chave
- ✅ "Tel: (11) 2222-3333" → TEM palavra-chave "Tel:"

### 4. **Checklist de Validação Rigoroso**
Expandido o checklist final com perguntas específicas:

**📞 VALIDAÇÃO DE TELEFONES:**
1. "Todos os telefones que inclui têm indicadores CLAROS (☎️📞📱📲 ou palavras-chave)?"
2. "Posso apontar EXATAMENTE onde cada telefone aparece no texto original?"
3. "Confirmei que NÃO são horários (11:30), endereços (123) ou anos (2023)?"
4. "Evitei inventar/deduzir telefones que não estão explícitos?"
5. "Se tive dúvida sobre algum número, deixei de fora?"

**🏢 VALIDAÇÃO GERAL:**
6. "Todos os dados que incluo estão LITERALMENTE presentes no texto?"
7. "Preferi usar null ao invés de adivinhar informações?"
8. "Removi nomes de cidades do nome da empresa?"
9. "Extraí apenas informações que tenho CERTEZA ABSOLUTA?"

### 5. **Lembrete Crítico Final**
Adicionado aviso destacado:
> ⚠️ "Se você não conseguir apontar EXATAMENTE onde um telefone aparece no texto com indicadores claros, então NÃO é um telefone válido. Deixe vazio."

### 6. **Exemplos de Quando Deixar Campos Vazios**
- Texto só tem horários: "Aberto das 11h às 22h" → telefones: []
- Texto só tem endereço: "Rua 15, nº 300" → telefones: []
- Texto só tem anos: "Desde 2020" → telefones: []
- Números sem contexto: "123 sabores disponíveis" → telefones: []

## Correções Técnicas
- Corrigidos erros de tipagem TypeScript nos parâmetros de funções
- Adicionadas tipagens explícitas para evitar erros de compilação

## Resultado Esperado
- ✅ IA não inventará mais telefones inexistentes
- ✅ Extração mais precisa e confiável de dados
- ✅ Menos dados incorretos inseridos no sistema
- ✅ Qualidade superior na geração de leads
- ✅ Redução significativa de telefones "fantasma"

## Localização das Alterações
- **Arquivo**: `/server/routes/leads.ts`
- **Método**: `POST /dadosig2` (linha 775-1110)
- **Seções modificadas**: Prompt de extração de dados (linhas 786-1043)

## Como Testar
1. Usar textos do Instagram que contenham apenas horários de funcionamento
2. Verificar se a IA não transforma horários em telefones
3. Testar com textos que só tenham endereços numéricos
4. Confirmar que campos de telefone ficam vazios quando apropriado