/**
 * Teste da implementação de múltiplos sócios no Bitrix
 * Executa: node test-bitrix-multiplos-socios.js
 */

const { CrmEmpresa } = require('./distServer/domain/crm/CrmEmpresa');
const Lead = require('./distServer/domain/crm/Lead').default;
const { BitrixServiceFactory } = require('./distServer/service/bitrix/BitrixService');

console.log('🧪 TESTE: Implementação de Múltiplos Sócios no Bitrix');
console.log('=' * 80);

// Cenário 1: Empresa com múltiplos sócios
function testarMultiplosSocios() {
  console.log('\n📋 TESTE 1: Empresa com múltiplos sócios');
  console.log('-' * 50);

  // Criar empresa
  const empresa = new CrmEmpresa('Antonio Pizzaria');
  empresa.id = 1;
  empresa.cnpj = '12345678000195';
  empresa.email = '<EMAIL>';
  empresa.telefone = '11999999999';

  // Adicionar sócios
  const socios = [
    {
      nome: '<PERSON> Silva',
      cargo: 'Sócio Fundador',
      dataEntrada: '01/01/2015',
      principal: true,
      scoreAnalise: 95,
      motivoSelecao: 'Primeiro sócio, cargo de direção, nome similar à empresa'
    },
    {
      nome: 'Maria Santos',
      cargo: 'Sócia Gerente',
      dataEntrada: '15/06/2018',
      principal: false,
      scoreAnalise: 70,
      motivoSelecao: 'Cargo gerencial, segunda na lista'
    },
    {
      nome: 'José Oliveira',
      cargo: 'Sócio',
      dataEntrada: '10/03/2020',
      principal: false,
      scoreAnalise: 45,
      motivoSelecao: 'Sócio comum'
    }
  ];

  empresa.adicionarSocios(socios);

  // Criar lead
  const lead = new Lead(
    1, // crmEmpresaId
    'Antonio Silva', // nomeResponsavel
    'Antonio Pizzaria', // empresa
    '11999999999', // telefone
    'antoniopizzaria', // instagramHandle
    'Pizzaria tradicional no bairro', // bioInsta
    'Instagram' // origem
  );
  lead.crmEmpresa = empresa;
  lead.segmento = 'Pizzaria';
  lead.cnpj = '12345678000195';

  console.log('✅ Lead criado com', lead.getQuantidadeSocios(), 'sócios');
  console.log('✅ Sócio principal:', lead.getSocioPrincipal()?.nome);
  console.log('✅ Sócios secundários:', lead.getSociosSecundarios().map(s => s.nome));
  console.log('✅ Todos os responsáveis:', lead.getTodosResponsaveis());

  return { lead, empresa };
}

// Cenário 2: Empresa sem sócios (fallback tradicional)
function testarSemSocios() {
  console.log('\n📋 TESTE 2: Empresa sem sócios (comportamento tradicional)');
  console.log('-' * 50);

  // Criar empresa sem sócios
  const empresa = new CrmEmpresa('João Lanches');
  empresa.id = 2;
  empresa.email = '<EMAIL>';
  empresa.telefone = '11888888888';

  // Criar lead
  const lead = new Lead(
    2, // crmEmpresaId
    'João Santos', // nomeResponsavel
    'João Lanches', // empresa
    '11888888888', // telefone
    'joaolanches', // instagramHandle
    'Lanchonete tradicional', // bioInsta
    'Instagram' // origem
  );
  lead.crmEmpresa = empresa;
  lead.segmento = 'Lanchonete';

  console.log('✅ Lead criado sem sócios');
  console.log('✅ hasSocios():', lead.hasSocios());
  console.log('✅ Responsável principal:', lead.getNomeResponsavelPrincipal());

  return { lead, empresa };
}

// Cenário 3: Empresa com sócio único
function testarSocioUnico() {
  console.log('\n📋 TESTE 3: Empresa com sócio único');
  console.log('-' * 50);

  // Criar empresa
  const empresa = new CrmEmpresa('Maria Café');
  empresa.id = 3;
  empresa.email = '<EMAIL>';

  // Adicionar sócio único
  const socio = {
    nome: 'Maria Costa',
    cargo: 'Proprietária',
    dataEntrada: '01/08/2020',
    principal: true,
    scoreAnalise: 100,
    motivoSelecao: 'Sócio único da empresa'
  };

  empresa.adicionarSocio(socio);

  // Criar lead
  const lead = new Lead(
    3, // crmEmpresaId
    'Maria Costa', // nomeResponsavel
    'Maria Café', // empresa
    '11777777777', // telefone
    'mariacafe', // instagramHandle
    'Café artesanal', // bioInsta
    'Instagram' // origem
  );
  lead.crmEmpresa = empresa;
  lead.segmento = 'Café';

  console.log('✅ Lead criado com sócio único');
  console.log('✅ Quantidade de sócios:', lead.getQuantidadeSocios());
  console.log('✅ Sócio principal:', lead.getSocioPrincipal()?.nome);

  return { lead, empresa };
}

// Função para simular chamada ao Bitrix (sem fazer chamada real)
function simularChamadaBitrix(lead) {
  console.log('\n🔄 SIMULAÇÃO: Criação no Bitrix para', lead.empresa);
  console.log('-' * 50);

  const bitrixService = BitrixServiceFactory.criarInstancia();

  // Simular a lógica sem fazer chamadas reais
  console.log('📞 Bitrix Service configurado');
  console.log('📊 Lead possui', lead.getQuantidadeSocios(), 'sócios');

  if (lead.hasSocios()) {
    console.log('🔄 Seria executado: criarMultiplosContatos()');
    lead.getSocios().forEach((socio, index) => {
      console.log(`   📝 Contato ${index + 1}: ${socio.nome} (${socio.principal ? 'Principal' : 'Secundário'})`);
      console.log(`       Cargo: ${socio.cargo || 'N/A'}`);
      console.log(`       Score: ${socio.scoreAnalise}%`);
    });
  } else {
    console.log('🔄 Seria executado: criarContato() (método tradicional)');
    console.log(`   📝 Contato único: ${lead.nomeResponsavel}`);
  }

  console.log('🔄 Seria executado: criarLead() com contatos vinculados');
  console.log('✅ Simulação concluída');
}

// Executar testes
try {
  const teste1 = testarMultiplosSocios();
  simularChamadaBitrix(teste1.lead);

  const teste2 = testarSemSocios();
  simularChamadaBitrix(teste2.lead);

  const teste3 = testarSocioUnico();
  simularChamadaBitrix(teste3.lead);

  console.log('\n' + '=' * 80);
  console.log('✅ TODOS OS TESTES CONCLUÍDOS COM SUCESSO!');
  console.log('📊 Funcionalidades testadas:');
  console.log('   ✓ Múltiplos sócios com contatos principais e secundários');
  console.log('   ✓ Fallback para comportamento tradicional sem sócios');
  console.log('   ✓ Tratamento de sócio único');
  console.log('   ✓ Métodos de acesso a sócios no Lead');
  console.log('   ✓ Integração com BitrixService');
  console.log('=' * 80);

} catch (error) {
  console.error('❌ ERRO NO TESTE:', error);
  console.error('Stack trace:', error.stack);
}