// Teste da rota dadosig2
// Execute com: node test-dadosig2.js

const axios = require('axios');

const textoInstagram = `AeLe Sushi
🥢 O Melhor da Comida Oriental em Gyn!
⏰ Almoço Sab (11:30 ás 14:00)
Jantar (18h às 22:30): Seg a Sáb.
⬇️ Conheça também nosso delivery. 🛵`;

async function testarDadosig2() {
  try {
    console.log('Testando rota /dadosig2...');

    const response = await axios.post('http://localhost:3000/api/leads/dadosig2', {
      texto: textoInstagram
    });

    console.log('Resposta da API:');
    console.log(JSON.stringify(response.data, null, 2));

    if (response.data.sucesso) {
      console.log('\n✅ Teste bem-sucedido!');
      console.log('\nLead criado:');
      const lead = response.data.dados;
      console.log('- ID da Empresa CRM:', lead.crmEmpresaId);
      console.log('- Nome Responsável:', lead.nomeResponsavel);
      console.log('- Empresa:', lead.empresa);
      console.log('- Telefone:', lead.telefone);
      console.log('- Instagram Handle:', lead.instagramHandle);
      console.log('- Seguidores:', lead.instagramData?.followers);
      console.log('- Categoria:', lead.instagramData?.businessCategory);
      console.log('- Notas:', lead.notas);

      if (lead.crmEmpresa) {
        console.log('\nEmpresa CRM:');
        console.log('- ID:', lead.crmEmpresa.id);
        console.log('- Nome:', lead.crmEmpresa.nome);
        console.log('- Telefone:', lead.crmEmpresa.telefone);
        console.log('- Endereço:', lead.crmEmpresa.endereco);
      }
    } else {
      console.log('\n❌ Teste falhou:', response.data.mensagem);
    }

  } catch (error) {
    console.error('❌ Erro no teste:', error.message);
    if (error.response) {
      console.error('Resposta do servidor:', error.response.data);
    }
  }
}

// Executar teste
testarDadosig2();
