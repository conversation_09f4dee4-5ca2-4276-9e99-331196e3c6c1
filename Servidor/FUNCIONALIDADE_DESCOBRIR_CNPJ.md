# Funcionalidade: Descobrir CNPJ da Empresa

## Resumo
Nova funcionalidade implementada que permite descobrir automaticamente o CNPJ de uma empresa a partir do nome utilizando busca no Google + IA.

## Como Funciona

### Fluxo do Sistema
1. **Usuário** digita o nome da empresa no formulário de novo lead
2. **Usuário** clica no botão "Descobrir CNPJ"
3. **Sistema** busca no Google: "[nome da empresa] CNPJ"
4. **IA (ChatGPT)** analisa os resultados e extrai o CNPJ
5. **Sistema** preenche automaticamente o campo CNPJ

### Componentes Implementados

#### Backend
- **SerpApiService**: Cliente para busca no Google via SerpAPI
- **CnpjDiscoveryService**: Orquestra o processo de descoberta
- **Nova rota**: `POST /crm/leads/descobrir-cnpj`

#### Frontend
- **Campo CNPJ**: Novo campo no formulário de lead
- **Botão "Descobrir CNPJ"**: Ao lado do campo empresa
- **Método descobrirCnpj()**: Lógica de chamada do serviço

### Configuração Necessária

#### 1. API Key do SerpAPI
No arquivo `/Servidor/server/config.json`, na seção de desenvolvimento:

```json
"cnpjDiscovery": {
  "serpApiKey": "SUA_API_KEY_AQUI",
  "maxResults": 15,
  "enabled": true
}
```

#### 2. Obter API Key
1. Acesse: https://serpapi.com/
2. Crie uma conta
3. Copie sua API Key
4. Cole no config.json

### Estrutura de Resposta

```typescript
interface DiscoveryResult {
  cnpj: string | null;           // "12.345.678/0001-90"
  nomeEmpresa: string | null;    // Nome encontrado
  razaoSocial: string | null;    // Razão social se encontrada
  fonte: string;                 // "google_search"
  confianca: 'alta' | 'media' | 'baixa';
  detalhes?: string;             // Explicação adicional
}
```

### Níveis de Confiança

- **Alta**: CNPJ encontrado em site oficial ou fonte confiável
- **Média**: CNPJ encontrado em sites de terceiros (consultacnpj.com, etc)
- **Baixa**: CNPJ não encontrado ou duvidoso

### Tratamento de Erros

O sistema trata graciosamente os seguintes cenários:
- SerpAPI não configurada
- Empresa não encontrada no Google
- CNPJ não identificado nos resultados
- Limite de requisições da API
- Erros na análise por IA

### Custo Estimado

- **SerpAPI**: ~$50/mês para 5.000 buscas
- **Plano básico suficiente** para a maioria dos casos

### Exemplo de Uso

1. Digite "Outback Steakhouse" no campo empresa
2. Clique "Descobrir CNPJ"
3. Sistema retorna: "04.704.816/0001-50"
4. CNPJ aparece automaticamente no formulário

### Limitações

- Depende da disponibilidade de informações públicas
- Funciona melhor com empresas conhecidas
- Precisão varia conforme a fonte dos dados
- Não consulta APIs oficiais (Receita Federal)

### Próximos Passos (Futuras Melhorias)

1. Integração com APIs oficiais (Brasil API, ReceitaWS)
2. Cache de resultados para evitar buscas repetidas
3. Busca por CNPJ em múltiplas fontes
4. Validação matemática do CNPJ

### Arquivos Modificados/Criados

#### Novos Arquivos
- `/Servidor/server/service/SerpApiService.ts`
- `/Servidor/server/service/CnpjDiscoveryService.ts`

#### Arquivos Modificados
- `/Servidor/server/routes/leads.ts` - Nova rota
- `/Servidor/src/app/crm/services/lead.service.ts` - Método descobrirCnpj()
- `/Servidor/src/app/crm/novo-lead/novo-lead.component.html` - UI
- `/Servidor/src/app/crm/novo-lead/novo-lead.component.ts` - Lógica
- `/Servidor/server/config.json` - Configurações

### Status
✅ Implementação completa
✅ Compilação bem-sucedida
⏳ Aguardando configuração da API Key para teste