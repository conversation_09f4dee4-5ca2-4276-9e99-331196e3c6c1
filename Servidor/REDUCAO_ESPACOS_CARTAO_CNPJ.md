# Redução de Espaços Verticais - Cartão Descobrir CNPJ

## Alterações Implementadas ✅

### 1. **HTML - Classes Bootstrap** (`novo-lead.component.html`)
- ✅ Adicionada classe `cnpj-discovery-card` ao cartão
- ✅ Reduzido `mt-4` → `mt-3` no divider "OU"
- ✅ Reduzido `mt-4` → `mt-3` na seção de busca manual
- ✅ Reduzido `mb-3` → `mb-2` no título da busca manual
- ✅ Reduzido `mb-3` → `mb-2` no botão de busca do Google

### 2. **CSS - Espaçamentos** (`novo-lead.component.scss`)

#### Classe Específica do Cartão CNPJ
```scss
.cnpj-discovery-card {
  .card-body {
    padding: 1.25rem; // reduzido de 1.5rem
  }
  
  .card-title {
    margin-bottom: 0.5rem; // reduzido de 1rem
  }
  
  .card-text {
    margin-bottom: 1rem; // reduzido de 1.25rem
  }
  
  .form-group {
    margin-bottom: 12px; // reduzido de 15px geral
  }
  
  .manual-search-section h6 {
    margin-bottom: 1rem !important; // forçar redução
  }
  
  .divider-or {
    margin: 15px 0; // reduzido de 20px geral
  }
}
```

#### Ajustes Globais
- ✅ `.form-group`: `margin-bottom` reduzido de 20px → 15px
- ✅ `.divider-or`: `margin` reduzido de 30px → 20px

## Resultado Final 📏

### Reduções Aplicadas:
- **Padding do card-body**: -4px (1.5rem → 1.25rem)
- **Margin do card-title**: -8px (1rem → 0.5rem)
- **Margin do card-text**: -5px (1.25rem → 1rem)
- **Form-groups**: -3px cada (15px → 12px) × 2 = -6px
- **Divider "OU"**: -10px (40px → 30px total no cartão)
- **Margins Bootstrap**: -8px cada (mt-4 → mt-3) × 2 = -16px
- **Margins mb**: -8px cada (mb-3 → mb-2) × 2 = -16px

### **Total Estimado**: ~65px de redução na altura do cartão

## Benefícios 🎯

1. **Visual mais compacto**: Cartão ocupa menos espaço vertical
2. **Melhor proporção**: Elementos mais equilibrados visualmente
3. **Mantém legibilidade**: Espaços ainda suficientes para boa leitura
4. **Experiência otimizada**: Menos scroll necessário para o usuário

## Como Testar 🧪

1. Navegar para o passo 3 (Descobrir CNPJ)
2. Comparar altura do cartão antes/depois
3. Verificar que todos os elementos estão bem espaçados
4. Confirmar que a funcionalidade permanece intacta

## Observações 📝

- As reduções foram cuidadosamente calibradas para manter usabilidade
- Classe específica `cnpj-discovery-card` permite ajustes isolados
- Outros cartões não são afetados pelas mudanças específicas
- Mudanças globais (form-group, divider-or) são sutis e melhoram todo o sistema