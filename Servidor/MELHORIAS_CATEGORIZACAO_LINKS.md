# Melhorias na Categorização de Links - Detecção de Concorrentes

## Problema Identificado

A categorização de links no sistema não estava detectando adequadamente quando um link era de um sistema concorrente de cardápio digital. Links de concorrentes como Goomer, MenuDigital, etc. estavam sendo categorizados como "Site do Cardápio" genérico, perdendo informação importante para análise competitiva.

## Soluções Implementadas

### 1. Nova Categoria "Concorrente"

- **Adicionado novo tipo**: `Concorrente` no enum `TipoLinkLead`
- **Ícone**: `fa-exclamation-triangle` (triângulo de alerta)
- **Cor**: `#ff6b35` (laranja de alerta)
- **Descrição padrão**: "Sistema Concorrente"

### 2. Base de Conhecimento Expandida

Criada lista abrangente de domínios concorrentes conhecidos:

```typescript
const CONCORRENTES_CONHECIDOS = [
  // Principais concorrentes
  'goomer.com.br', 'goomer.app',
  'cardapio.delivery', 'cardapiodelivery.com',
  'menudigital.com', 'menudigital.com.br',
  'qrmenu.com.br', 'meniuapp.com',
  'grandchef.com.br', 'mistercheff.com.br',
  'consumer.com.br', 'saipos.com',
  // ... e muitos outros
];
```

### 3. Função de Detecção Inteligente

Implementada função `isUrlConcorrente()` que:
- Verifica domínios exatos da lista de concorrentes
- Analisa padrões na URL (combinações de palavras-chave)
- Detecta URLs que combinam termos como: cardapio + digital/qr/online

### 4. Prompts de IA Melhorados

**Antes:**
```
- Site do Cardápio: Sites de cardápio online (goomer.app, cardapio.delivery, etc.)
```

**Depois:**
```
- Site do Cardápio: Sites de cardápio online próprios ou neutros
- Concorrente: Sistemas de cardápio digital concorrentes (goomer, cardapio.delivery, menudigital, etc.)

DOMÍNIOS CONCORRENTES CONHECIDOS:
goomer.com.br, cardapio.delivery, menudigital.com, qrmenu.com.br...

REGRAS PARA DETECTAR CONCORRENTES:
- URLs que contenham domínios da lista = Concorrente
- URLs com padrões: cardapio + digital/qr/online = Concorrente
- Se houver dúvida, prefira "Concorrente"
```

### 5. Validação Dupla

Implementado sistema de validação em duas camadas:
1. **IA categoriza** com prompt melhorado
2. **Função auxiliar verifica** se algo foi perdido e corrige

```typescript
// Validação adicional após IA
linksCategorized = linksCategorized.map((link: any) => {
  if (link.tipo === 'Site do Cardápio' && isUrlConcorrente(link.url)) {
    return {
      ...link,
      tipo: 'Concorrente',
      descricao: 'Sistema Concorrente',
      ordem: 2 // Alta prioridade
    };
  }
  return link;
});
```

### 6. Priorização de Concorrentes

- **Ordem de relevância atualizada**: WhatsApp=1, **Concorrente=2**, Site=3, Instagram=4
- Concorrentes agora têm prioridade alta na exibição
- Descrições específicas: "Sistema Goomer", "Sistema MenuDigital"

## Arquivos Modificados

### Backend
- `Servidor/server/domain/crm/LeadLink.ts` - Novo enum e métodos
- `Servidor/server/routes/leads.ts` - Lógica de detecção e prompts

### Frontend
- `Servidor/src/app/crm/lead-crud/lead-crud.component.ts` - Tipos de link
- `Servidor/src/app/crm/lead-info/lead-info.component.ts` - Exibição
- `Servidor/src/app/crm/novo-lead/novo-lead.component.html` - Template

## Benefícios

1. **Detecção Precisa**: Identifica corretamente sistemas concorrentes
2. **Análise Competitiva**: Facilita identificação de leads usando concorrentes
3. **Priorização**: Concorrentes têm destaque visual e prioridade
4. **Escalabilidade**: Fácil adicionar novos concorrentes à lista
5. **Robustez**: Validação dupla (IA + função auxiliar)

## Como Usar

1. **Análise automática**: Links são categorizados automaticamente
2. **Identificação visual**: Concorrentes aparecem com ícone de alerta laranja
3. **Prioridade**: Concorrentes aparecem no topo da lista de links
4. **Descrição clara**: "Sistema [Nome]" para fácil identificação

## Próximos Passos Sugeridos

1. **Monitoramento**: Acompanhar eficácia da detecção
2. **Expansão**: Adicionar novos concorrentes conforme descobertos
3. **Relatórios**: Criar dashboards de análise competitiva
4. **Alertas**: Notificar quando lead usa concorrente específico
