# Melhorias no BitrixService - Envio de Links Categorizados

## Problema Identificado

O BitrixService não estava enviando corretamente os links categorizados dos leads para o Bitrix24. Os campos personalizados do Bitrix para diferentes tipos de links (Instagram, Site, Concorrente, iFood) não estavam sendo populados adequadamente.

## Campos Personalizados do Bitrix

Mapeamento dos campos personalizados fornecidos:

- **Instagram**: `UF_CRM_1615222177542`
- **Site**: `UF_CRM_1621947262974`  
- **Concorrente**: `UF_CRM_1623263814`
- **iFood**: `UF_CRM_1623334200`

## Soluções Implementadas

### 1. Adição da Propriedade `links` ao Lead

```typescript
// Servidor/server/domain/crm/Lead.ts
export default class Lead {
  // ... outras propriedades
  links?: any[]; // Array de LeadLink associados
}
```

### 2. Métodos Utilitários para Extrair Links

Adicionados métodos ao Lead para facilitar acesso aos links por tipo:

```typescript
// Métodos para extrair links específicos por tipo
getLinkPorTipo(tipo: string): string | null
getLinkInstagram(): string | null
getLinkSite(): string | null  
getLinkConcorrente(): string | null
getLinkIfood(): string | null
getLinkWhatsApp(): string | null
getLinkLocalizacao(): string | null
```

### 3. Atualização dos Mapeamentos do BitrixService

**Antes:**
```typescript
{
  label: "Instagram",
  fieldName: "UF_CRM_1615222177542", 
  leadProperty: "linkInsta" // Propriedade fixa
}
```

**Depois:**
```typescript
{
  label: "Instagram",
  fieldName: "UF_CRM_1615222177542",
  leadProperty: "getLinkInstagram", // Método que busca dinamicamente
  transform: (value) => value || ''
}
```

### 4. Suporte a Métodos no `getNestedProperty`

Atualizado para suportar chamadas de métodos além de propriedades:

```typescript
private getNestedProperty(obj: any, path: string): any {
  // Se o path é um método, tenta chamar como método
  if (!path.includes('.') && typeof obj[path] === 'function') {
    try {
      return obj[path]();
    } catch (error) {
      console.warn(`BITRIX: Erro ao chamar método ${path}:`, error);
      return undefined;
    }
  }
  
  // Caso contrário, navega pelas propriedades aninhadas
  return path.split('.').reduce((current, prop) => {
    return current && current[prop] !== undefined ? current[prop] : undefined;
  }, obj);
}
```

### 5. Carregamento de Links nas Rotas

Atualizado para carregar links antes de enviar para Bitrix:

```typescript
// Carregar links se o lead foi salvo e tem ID
if (leadParaBitrix.id) {
  const mapeadorLink = new MapeadorDeLeadLink();
  leadParaBitrix.links = await mapeadorLink.buscarPorLead(leadParaBitrix.id);
}
```

### 6. Logs Melhorados

Adicionados logs detalhados para debug:

```typescript
console.log('BITRIX: Lead possui', lead.links?.length || 0, 'links associados');
console.log('BITRIX: Tentando mapear ${mapping.label} (${mapping.leadProperty}): ${value}');
console.log('BITRIX: ✓ Custom field mapeado: ${mapping.fieldName} (${mapping.label}) = ${value}');
```

## Mapeamento Completo dos Campos

| Tipo de Link | Campo Bitrix | Método Lead | Descrição |
|--------------|--------------|-------------|-----------|
| Instagram | `UF_CRM_1615222177542` | `getLinkInstagram()` | Link do perfil Instagram |
| Site | `UF_CRM_1621947262974` | `getLinkSite()` | Site oficial da empresa |
| Concorrente | `UF_CRM_1623263814` | `getLinkConcorrente()` | Sistema concorrente detectado |
| iFood | `UF_CRM_1623334200` | `getLinkIfood()` | Link do cardápio no iFood |

## Lógica de Priorização

### Instagram
1. Link categorizado como "Instagram" 
2. Propriedade `linkInsta` (bio do Instagram)
3. URL gerada com `instagramHandle`

### Site  
1. Link categorizado como "Site"
2. Propriedade `instagramData.website`

### Concorrente
1. Link categorizado como "Concorrente"

### iFood
1. Link categorizado como "Ifood"

## Arquivos Modificados

### Backend
- `Servidor/server/domain/crm/Lead.ts` - Adicionada propriedade `links` e métodos utilitários
- `Servidor/server/service/bitrix/BitrixService.ts` - Mapeamentos atualizados e suporte a métodos
- `Servidor/server/routes/leads.ts` - Carregamento de links antes do envio

## Como Funciona

1. **Lead é criado** com links categorizados
2. **Links são carregados** do banco antes do envio ao Bitrix
3. **Métodos utilitários** extraem links específicos por tipo
4. **BitrixService mapeia** automaticamente para campos personalizados
5. **Campos são enviados** para Bitrix24 com valores corretos

## Benefícios

1. **Mapeamento Automático**: Links são enviados automaticamente para campos corretos
2. **Priorização Inteligente**: Sistema escolhe o melhor link disponível por tipo
3. **Flexibilidade**: Suporte a múltiplos links do mesmo tipo
4. **Logs Detalhados**: Facilita debug e monitoramento
5. **Compatibilidade**: Mantém funcionamento com dados legados

## Teste e Validação

Para testar o envio correto:

1. Criar lead com links categorizados
2. Sincronizar com Bitrix via rota `/sincronizar-bitrix`
3. Verificar logs para confirmar mapeamento
4. Validar no Bitrix24 se campos foram populados

## Próximos Passos

1. **Monitoramento**: Acompanhar logs de sincronização
2. **Validação**: Confirmar dados no Bitrix24
3. **Otimização**: Melhorar performance se necessário
4. **Expansão**: Adicionar novos tipos de links conforme necessário
