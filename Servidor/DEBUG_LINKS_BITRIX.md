# Debug - Links não sendo enviados para Bitrix

## Problema Identificado

Pelos logs fornecidos, os métodos `getLinkIfood()` e `getLinkConcorrente()` estão retornando `undefined`, indicando que:

1. **Os links não estão sendo carregados** do banco de dados
2. **O objeto Lead não tem os métodos** que criamos (problema de instanciação)

## Aná<PERSON><PERSON> dos Logs

```
BITRIX: Tentando mapear Link Ifood (getLinkIfood): undefined
BITRIX: ✗ Valor vazio para Link Ifood (getLinkIfood)
BITRIX: Tentando mapear Site do Concorrente (getLinkConcorrente): undefined
BITRIX: ✗ Valor vazio para Site do Concorrente (getLinkConcorrente)
```

## Causa Raiz Identificada

O problema está na **instanciação do objeto Lead**. O MyBatis retorna um objeto JavaScript simples, não uma instância da classe `Lead`. Portanto, os métodos que adicionamos (`getLinkInstagram()`, `getLinkIfood()`, etc.) não existem no objeto.

## Soluções Implementadas

### 1. Conversão para Instância da Classe Lead

**Antes:**
```typescript
const leadParaBitrix = novo?.id ? novo : obj;
// leadParaBitrix é um objeto simples do MyBatis
```

**Depois:**
```typescript
// Converter para instância da classe Lead
const leadInstance = new Lead(
  leadParaBitrix.crmEmpresaId,
  leadParaBitrix.nomeResponsavel,
  leadParaBitrix.empresa,
  leadParaBitrix.telefone,
  leadParaBitrix.instagramHandle,
  leadParaBitrix.bioInsta,
  leadParaBitrix.origem
);

// Copiar todas as propriedades
Object.assign(leadInstance, leadParaBitrix);

// Usar a instância da classe
leadParaBitrix = leadInstance;
```

### 2. Logs Detalhados Adicionados

**Na rota de criação:**
```typescript
console.log('LEADS: Carregando links para lead ID:', leadParaBitrix.id);
console.log('LEADS: Links carregados:', leadParaBitrix.links);
leadParaBitrix.links.forEach((link: any, index: number) => {
  console.log(`LEADS: Link ${index + 1}: ${link.tipo} = ${link.url}`);
});
```

**No BitrixService:**
```typescript
console.log('BITRIX: Lead possui', lead.links?.length || 0, 'links associados');
console.log('BITRIX: Testando métodos do Lead:');
console.log('BITRIX: getLinkInstagram():', lead.getLinkInstagram());
console.log('BITRIX: getLinkIfood():', lead.getLinkIfood());
```

### 3. Rota de Teste Criada

Nova rota `/teste-links-bitrix` que:
- Cria um Lead com links categorizados
- Testa todos os métodos de extração de links
- Envia para Bitrix para validação

## Fluxo Corrigido

1. **Lead é criado** no banco de dados
2. **Links são salvos** na tabela `crm_lead_link`
3. **Links são carregados** do banco
4. **Objeto é convertido** para instância da classe Lead
5. **Métodos funcionam** corretamente (`getLinkIfood()`, etc.)
6. **BitrixService mapeia** os links para campos personalizados
7. **Dados são enviados** para Bitrix24

## Arquivos Modificados

- `Servidor/server/routes/leads.ts` - Conversão para instância da classe e logs
- `Servidor/server/service/bitrix/BitrixService.ts` - Logs detalhados
- Nova rota de teste adicionada

## Como Testar

### 1. Teste com Rota Específica
```
GET /crm/leads/teste-links-bitrix
```

### 2. Verificar Logs
Procurar por:
- `LEADS: Links carregados:` - deve mostrar array de links
- `BITRIX: getLinkIfood():` - deve mostrar URL ou null
- `BITRIX: ✓ Custom field mapeado:` - deve mostrar campos enviados

### 3. Validar no Bitrix24
Verificar se os campos personalizados foram populados:
- Instagram: `UF_CRM_1615222177542`
- Site: `UF_CRM_1621947262974`
- Concorrente: `UF_CRM_1623263814`
- iFood: `UF_CRM_1623334200`

## Próximos Passos

1. **Executar teste** com a nova rota
2. **Verificar logs** para confirmar funcionamento
3. **Validar no Bitrix** se campos foram populados
4. **Aplicar correção** em outras partes do sistema se necessário

## Observações Importantes

- **Problema de instanciação** é comum com ORMs como MyBatis
- **Object.assign()** preserva todas as propriedades do objeto original
- **Conversão é necessária** sempre que precisarmos dos métodos da classe
- **Performance** não é impactada significativamente pela conversão
