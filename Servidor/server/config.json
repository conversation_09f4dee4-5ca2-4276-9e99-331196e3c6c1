{"ambiente": {"desenvolvimento": {"urlSite": "localhost:8443", "urlCardapio": "localhost:8443", "ip": "*************", "protocolo": "http", "versao_mysql": "5.5", "urlImagens": "http://localhost:3000/images/", "urlLogin": "https://localhost:8443", "urlViral": "http://participeai.com.br/promo/", "caminhoAutorizacaoGoogle": "C:\\Trabalho\\promokit\\Servidor\\promokitpedidos-ypbowr-firebase-adminsdk-26iip-e5ef896fbb.json", "caminhoArquivos": "/home/<USER>/Trabalho/promokit/Servidor/distServer/images", "caminhoImagens": "/home/<USER>/Trabalho/promokit/Servidor/distServer/images", "aplicativoFacebook": {"appId": "****************", "appSecret": "47e2561b493b5afd67998391dc88cbb0", "paginaSorteieme": "***************", "accountKitSecret": "3ab219f3c635b162e812ca8524f7bd6b"}, "cnpjDiscovery": {"serpApiKey": "sua_serp_api_key_aqui", "maxResults": 15, "enabled": true}}, "marcio": {"urlSite": "localhost:8443", "urlCardapio": "localhost:8443", "protocolo": "http", "versao_mysql": "8.0", "urlImagens": "http://*************:3000/images/", "urlLogin": "https://*************:8443", "urlViral": "https://localhost:8443/promo/", "caminhoArquivos": "/home/<USER>/Downloads/promokit2/Servidor/server/public/images", "caminhoImagens": "/home/<USER>/Downloads/promokit2/Servidor/server/public/images", "caminhoAutorizacaoGoogle": "C:\\Trabalho\\promokit\\Servidor\\promokitpedidos-ypbowr-firebase-adminsdk-26iip-e5ef896fbb.json", "ambienteDialogFlow": "<PERSON><PERSON>", "aplicativoFacebook": {"appId": "****************", "appSecret": "47e2561b493b5afd67998391dc88cbb0", "accountKitSecret": "3ab219f3c635b162e812ca8524f7bd6b", "paginaSorteieme": "***************"}}, "novaversao": {"urlSite": "localhost:3000", "urlImagens": "https://www.sorteiefb.com.br/images/promocoes/", "urlLogin": "https://novo.sorteiefb.com.br/", "urlViral": "http://participeai.com.br/promo/", "versao_mysql": "8.0", "caminhoArquivos": "/sorteie/web/static", "caminhoImagens": "/sorteie/web/static/images/promocoes", "aplicativoFacebook": {"appId": "***************", "appSecret": "fa61f0c90e5e21f885e363219d1e519c", "accountKitSecret": "d170239556a144e6ac9144d5d1579dae", "paginaSorteieme": "***************"}}, "producao": {"urlSite": "promokit.com.br", "ip": "*************", "urlCardapio": "meucardapio.ai", "protocolo": "https", "urlImagens": "http://localhost:3000/images/", "versao_mysql": "8.0", "urlLogin": "https://localhost:8443", "urlViral": "http://participeai.com.br/promo/", "caminhoAutorizacaoGoogle": "/sorteie/git/promokit/Servidor/promokitpedidos-ypbowr-firebase-adminsdk-26iip-e5ef896fbb.json", "ambienteDialogFlow": "Producao", "caminhoArquivos": "/sorteie/git/promokit/static/arquivos", "caminhoImagens": "/sorteie/git/promokit/static/images", "aplicativoFacebook": {"appId": "****************", "appSecret": "47e2561b493b5afd67998391dc88cbb0", "paginaSorteieme": "***************", "accountKitSecret": "3ab219f3c635b162e812ca8524f7bd6b"}}, "homologacao": {"urlSite": "promokit.com.br", "ip": "*************", "urlCardapio": "meucardapio.ai", "protocolo": "http", "urlImagens": "http://localhost:3000/images/", "versao_mysql": "5.5", "urlLogin": "https://localhost:8443", "urlViral": "http://participeai.com.br/promo/", "caminhoAutorizacaoGoogle": "/sorteie/git/promokit/Servidor/promokitpedidos-ypbowr-firebase-adminsdk-26iip-e5ef896fbb.json", "ambienteDialogFlow": "Homologacao", "caminhoArquivos": "/sorteie/git/promokit/static/arquivos", "caminhoImagens": "/sorteie/git/promokit/static/images", "aplicativoFacebook": {"appId": "***************", "appSecret": "********************************", "paginaSorteieme": "***************", "accountKitSecret": "3ab219f3c635b162e812ca8524f7bd6b"}}}, "pagseguro": {"loginPagSeguro": "rafael.fibon<PERSON><PERSON>@solucoesageis.com.br", "tokenPagseguro": {"producao": "7DDC7F9952A8416AAA4554ED49EE8EF9", "sandbox": "********************************"}, "host": {"producao": "pagseguro.uol.com.br", "sandbox": "sandbox.pagseguro.uol.com.br"}}, "facebook": {"adminPermissions": ["pages_show_list", "email", "user_birthday", "user_location", "manage_pages"], "userPermissions": ["email", "user_birthday", "user_location"], "pagePermissions": ["manage_pages"], "basicPermissions": ["email"]}}