import {<PERSON>tra<PERSON>} from "../domain/faturamento/Contrato";
import {MapeadorDeContrato} from "../mapeadores/MapeadorDeContrato";
import {EnumTipoLancamento} from "../lib/emun/EnumTipoLancamento";
import {Fatura} from "../domain/faturamento/Fatura";
import {Lancamento} from "../domain/faturamento/Lancamento";
import {Pagamento} from "../domain/faturamento/Pagamento";
import {EnumStatusPagamento} from "../lib/emun/EnumStatusPagamento";
import {MapeadorDeFatura} from "../mapeadores/MapeadorDeFatura";
import * as  _ from 'underscore';
import {Assinatura} from "../domain/faturamento/Assinatura";
import {EnumFaturaIugu} from "../lib/emun/EnumFaturaIIugu";
import {MapeadorDeAssinatura} from "../mapeadores/MapeadorDeAssinatura";
import {IuguService} from "./IuguService";
import {EnumStatusFatura} from "../lib/emun/EnumStatusFatura";
// @ts-ignore
import moment = require("moment");
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {LogFatura} from "../domain/faturamento/LogFatura";
import {MapeadorDePlanoEmpresarial} from "../mapeadores/MapeadorDePlanoEmpresarial";
import {RegistroDeOperacaoService} from "./RegistroDeOperacaoService";
import {Ambiente} from "./Ambiente";

export class ContratoService {
  private registroDeOperacaoService: RegistroDeOperacaoService;
  salveContrato(contrato: Contrato): Promise<string> {
    return new Promise<any>(async (resolve, reject) => {
      if (!contrato.empresa) return resolve('Empresa não informada')
      if (!contrato.plano) return resolve('Plano não informado')
      if (!contrato.diaVencimento) return resolve('Dia de vencimento não informado')
      if (contrato.diaVencimento <= 0 || contrato.diaVencimento > 31)
        return resolve('Dia de vencimento inválido: ' + contrato.diaVencimento)

      if(contrato.valorNegociado && contrato.valorNegociado > contrato.plano.valor)
        return resolve('Valor negóciado dever ser menor que o valor do Plano: R$' + contrato.plano.valor)

      if (contrato.dataAtivacao && moment().diff(moment(contrato.dataAtivacao), 'd') > 60)
        return resolve('Data de ativação inválida, não pode ser maior que  60 dias.')

      if(!contrato.empresa.cnpj && !contrato.empresa.responsavel)
        return  resolve('Empresa não tem CNPJ nem responsável cadastrado')

      if(contrato.empresa.responsavel && !contrato.empresa.responsavel.cpf)
        return  resolve('Responsável pela empresa não tem CPF cadastrado')

      let mapeador = new MapeadorDeContrato();

      let existe = await mapeador.existeSync({idEmpresa: contrato.empresa.id });

      if(existe)  return resolve('Já existe um contrato para esse empresa')

      mapeador.transacao(async  (conexao: any, commit: Function) => {

        await mapeador.insiraSync(contrato);

        commit( () => {resolve('') })
      })
    });
  }

  async alterePlano(contrato: Contrato, plano: any){
    return new Promise<any>(async (resolve, reject) => {
      if(contrato.plano.id  === plano.id)
        return resolve('Assinatura já esta nesse plano: ' + plano.nome)

      let subscription: any =
        await new IuguService().alterePlanoDaAssinatura(contrato, plano).catch( erro => {
          resolve(erro)
        })

      if(subscription){
        await this.sincronizeAssinaturaIugu(contrato, subscription)
        resolve('');
      }


    });
  }

  async altereDataVencimento(contrato: Contrato, dataVencimento: Date, operador: any) {
    return new Promise<string>(async (resolve, reject) => {
       try{

         let erroIugu: string =
            await new IuguService().altereDataVencimentoAssinatura(contrato.assinatura, dataVencimento);

         if(erroIugu) return resolve(erroIugu)

         let diaVencimento =  dataVencimento.getDate();

         if(contrato.diaVencimento !== Number(diaVencimento))
           await contrato.atualizeDiaVencimento(diaVencimento, operador)

         if(dataVencimento)
           await contrato.atualizeProximoVencimento(dataVencimento, 'Data próximo vencimento atualizada', operador);

         resolve(null)
       } catch (err){
         console.error(err)
         resolve(err.message ? err.message : err)
       }
    })
  }

  salveLancamento(fatura: Fatura, dados: any): Promise<string> {
    return new Promise<string>(async (resolve, reject) => {
      if(!fatura.podeAlterar())
        return resolve('Fatura não pode ser alterada, status atual')

      let  novoLacamento = fatura.insiraLancamento(dados.descricao, EnumTipoLancamento.Cobranca,
        dados.qtde, dados.servicoCobrado, dados.valor, dados.desconto);

      await novoLacamento.insira();

      resolve('');

    })
  }

  excluaLancamento(fatura: Fatura, lancamento: any): Promise<string> {
    return new Promise<any>(async (resolve, reject) => {
      if(!fatura.podeAlterar())
        return resolve('Fatura não pode ser alterada, status atual')

      let lancamentoRemover: Lancamento =  _.findWhere(fatura.lancamentos, {id: lancamento.id})

      if(!lancamentoRemover) return resolve('Lancamento inválido: ' + lancamento.id);

      await lancamentoRemover.remova();

      fatura.lancamentos.splice(fatura.lancamentos.indexOf(lancamentoRemover), 1);

      resolve('');

    })
  }

  async mudouStatusFatura(contrato: Contrato, fatura: Fatura, novoStatus: any, motivo: string = null){
    if(novoStatus == null)
         throw new Error("Status do pagamento inválido");

    return new Promise<any>(async (resolve, reject) => {
      new MapeadorDeContrato().transacao(async  (conexao: any, commit: Function) => {

        if(fatura.status !== novoStatus) {
          console.log(String(`Novo status da fatura ${fatura.codigo}: ${novoStatus}`));
          fatura.status = novoStatus;
          if( fatura.status === EnumStatusFatura.Paga){
            await contrato.efetuouPagamento(fatura);
          } else if( fatura.status === EnumStatusFatura.Reembolsada){
            await contrato.reembolsouPagamento(fatura);
          } else if( fatura.status === EnumStatusFatura.Contestada || fatura.status === EnumStatusFatura.Chargeback  ){
            await contrato.bloqueiEmpresaPorChargeback(fatura);
          }
        } else {
          console.log('Fatura já está nesse status: ' + novoStatus)
        }

        if(motivo) fatura.observacao = motivo;

        await fatura.atualize();

        commit( () => { resolve('')})
      })
    })
  }

  async mudouStatusPagamento(pagamento: Pagamento, novoStatus: EnumStatusPagamento, motivo: string = null): Promise<string> {
    if(!novoStatus)
      return "Status do pagamento inválido";

    let erro: string;
    if(pagamento.status !== novoStatus) {
      console.log('Pagamento ' + pagamento.id + ' foi marcado como ' + novoStatus);
      if(novoStatus === EnumStatusPagamento.Aprovado){
        erro = await this.confirmePagamento(pagamento);
      } else if(novoStatus === EnumStatusPagamento.Cancelado){
        erro = await this.cancelePagamento(pagamento, motivo);
      } else if(novoStatus === EnumStatusPagamento.Reembolsado) {
        erro = await this.reembolsePagamento(pagamento, motivo);
      }else if(novoStatus === EnumStatusPagamento.EmAnalise){
        await pagamento.atualizeStatus(EnumStatusPagamento.EmAnalise);
      } else {
        erro = 'Pagamento com status não esperado:' + novoStatus;
      }
    } else {
      console.log('Pagamento já está nesse status: ' + novoStatus)
    }

    return erro;
  }

  async confirmePagamento(pagamento: Pagamento): Promise<any> {

    return new Promise<any>(async (resolve, reject) => {
      let fatura: Fatura = await  Fatura.get(pagamento.fatura.id);

      new MapeadorDeFatura().transacao(async  (conexao: any, commit: Function) => {
        await fatura.atualizePaga(pagamento);
        await pagamento.atualizeStatus(EnumStatusPagamento.Aprovado);
        await fatura.contrato.efetuouPagamento(fatura);

        commit( () => {
            resolve('');
        });
      })
    })
  }

  async cancelePagamento(pagamento: Pagamento, motivo: string): Promise<string>{
    return new Promise<any>(async (resolve, reject) => {

      let erro: string = await pagamento.cancele(motivo);

      resolve(erro);
    })
  }

  async reembolsePagamento(pagamento: Pagamento, motivo: string): Promise<string>{
    return new Promise<any>(async (resolve, reject) => {
      //TODO executar reembolso ainda
      resolve('Executar reembolso pagamento pendente')
    })
  }

  async sincronizeFaturaIugu( faturaIugu: any, contrato: Contrato = null, empresa: any = null){
    console.log('sincronizar fatura: ' + faturaIugu.id);
    let faturaExistente =    await Fatura.get( { codigo: faturaIugu.id , avulso: !contrato } );

    let invoice: any = await new IuguService().obtenhaFatura(faturaIugu.id);

    if(!invoice) {
      console.log('Fatura não existe: ' + faturaIugu.idEmpresa)
      return faturaExistente
    }

    let dataVencimento = moment(invoice.due_date).toDate(),
      dataPagamento = invoice.paid_at ? moment(invoice.paid_at).toDate() : null,
      referencia: number = Number(moment(dataVencimento).format('YYYYMM')),
      status = invoice.status,
      boleto = invoice.bank_slip,
      pix = invoice.pix,
      valorPago = invoice.paid_cents / 100,
      lancamentos = invoice.items,
      dataAtualizacao = invoice.updated_at ?  moment(invoice.updated_at).toDate()  : null,
      secure_url = invoice.secure_url;


    let dados: any = { link: secure_url, lancamentos: lancamentos, parcelas: invoice.installments, valorPago: valorPago,
                       status: status,  total: invoice.total, formaDePagamento: invoice.payable_with
    };

    if(boleto) dados.boleto = boleto
    if(pix && pix.qrcode) dados.pix = pix

    let logsFatura = [];

    if(invoice.logs){
      let logPagamento: any = _.find(invoice.logs, { description:  'Fatura paga com sucesso!'})

      if(logPagamento) dados.descricaoPagamento = logPagamento.notes;

      logsFatura = invoice.logs.map ((log: any) => new LogFatura(log.id, log.description, log.notes, log.created_at))

    }

    if( !faturaExistente ) {
      let fatura: Fatura = new Fatura(contrato, referencia, dataVencimento, dados );
      fatura.codigo = invoice.id;
      // @ts-ignore
      fatura.status = EnumFaturaIugu[status];
      if(fatura.status >= 0){
        fatura.dataAtualizacao =  dataAtualizacao;
        fatura.dataPagamento =  dataPagamento;
        fatura.logs =  logsFatura;
        fatura.maxParcelas = invoice.max_installments_value;
        fatura.valorPago = valorPago;

        if(empresa)
          fatura.empresa = empresa;

        let totalpagas: number = await new MapeadorDeFatura().selecioneTotalPagas(fatura.empresa);

        fatura.primeira =  totalpagas === 0

        if(contrato && contrato.id)
          await contrato.gerouNovaFatura(fatura)
        else
          await  fatura.insira();
        return fatura;
      } else {
        console.log(String(`Não vai sincronizar fatura ${fatura.codigo}, status não esperado: ${status}`));
        return null;
      }
    } else {
      let novoStatus: any = EnumFaturaIugu[status];
      faturaExistente.dataAtualizacao = dataAtualizacao;
      faturaExistente.dataPagamento =  dataPagamento;
      faturaExistente.dados = dados;
      faturaExistente.logs =  logsFatura;
      faturaExistente.maxParcelas = invoice.max_installments_value;
      faturaExistente.valorPago = valorPago;

      if(!contrato)  contrato = faturaExistente.contrato;

      if(contrato && contrato.id)
        await this.mudouStatusFatura(contrato, faturaExistente, novoStatus);
      else
        faturaExistente.status = novoStatus;

      return faturaExistente;
    }

  }

  async vinculeAhAssinaturaPai(contrato: Contrato, assinaturaPai: Assinatura) {
    return new Promise<any>(async (resolve, reject) => {
      new MapeadorDeAssinatura().transacao( async (conexao: any, commit: any) => {
        let assinatura: Assinatura = new Assinatura(contrato.empresa,  null);

        assinatura.codigoPai = assinaturaPai.codigo;
        assinatura.ativa = assinaturaPai.ativa;
        assinatura.suspensa = assinaturaPai.suspensa;
        assinatura.dataCriacao = assinaturaPai.dataCriacao;
        assinatura.dataAtualizacao = assinaturaPai.dataAtualizacao;
        assinatura.identificadorPlano = assinaturaPai.contrato.plano.identificador;
        assinatura.dataVencimento = assinaturaPai.dataVencimento;
        assinatura.dados = {    faturas_recentes: [], subitems: []  }
        assinatura.formasDePagamento = assinaturaPai.formasDePagamento;


        await assinatura.insira();
        await contrato.vinculeAssinatura(assinatura);

        contrato.empresa.dataBloqueioAuto = assinaturaPai.contrato.empresa.dataBloqueioAuto;
        contrato.empresa.bloqueada = assinaturaPai.contrato.empresa.bloqueada;

        await new MapeadorDeEmpresa().atualizeDadosBloqueio(contrato.empresa);

        commit( () => {
          resolve(assinatura)
        })
      })
    })
  }

  async sincronizeAssinaturaIugu(contrato: Contrato, subscription: any) {
    return new Promise<any>(async (resolve, reject) => {
      if(contrato.assinatura && contrato.assinatura.codigoPai)
         return  reject('Assinatura deve ser sincronizada apenas no contrato principal.')

      console.log('Sincronizar assinatura do contrato: ' + contrato.id);
      console.log(subscription)
      if(contrato.assinatura && contrato.assinatura.codigo !== subscription.id)
        return  reject('Contrato já tem uma assinatura associada: ' +  contrato.assinatura.codigo);

      new MapeadorDeAssinatura().transacao( async (conexao: any, commit: any) => {
        let  assinatura = new Assinatura(contrato.empresa, subscription),
             nova = false;

        if(!assinatura.formasDePagamento)
          return reject('Forma de pagamento da assinatura inválida: ' + subscription.payable_with)

        if( !contrato.assinatura ){
          nova = true;
          await assinatura.insira();
          await contrato.vinculeAssinatura(assinatura);
        } else {
          assinatura.id = contrato.assinatura.id;
        }

        if(!contrato.empresa.codigoCliente){
          contrato.empresa.codigoCliente = subscription.customer_id;
          await new MapeadorDeEmpresa().atualizeCliente(contrato.empresa);
        }

        if(contrato.empresa.codigoCliente !== subscription.customer_id)
          return reject('Empresa já está associado a outro customer_id: ' + contrato.empresa.codigoCliente )

        let faturasRecentes: any = assinatura.obtenhaFaturasRecentes();

        if( faturasRecentes ){
          for(let i = 0; i < faturasRecentes.length; i++){
            let faturaIugu = faturasRecentes[i];

            await this.sincronizeFaturaIugu(faturaIugu, contrato);
          }
        }

        if(!contrato.empresa.dataBloqueioAuto)
         await contrato.atualizeVencimentoBloqueio()

        if(contrato.plano.identificador !== assinatura.identificadorPlano){
          let plano = await new MapeadorDePlanoEmpresarial().selecioneSync({identificador: assinatura.identificadorPlano})
          console.log(String(`Assinatura ${assinatura.codigo} mudou para o plano ${ plano.nome} `))
          await contrato.atualizePlano(plano)
        }

        if(!nova)
          await assinatura.atualize();

        commit(() => {
          resolve(assinatura)
        })
      })
    })

  }

  async executeCobrancasCartao(){
    let faturas: any =  await new MapeadorDeFatura().listeFaturasPagarNoCartao();
    console.log('total faturas executar cobrança no cartao: ' + faturas.length)
    if(faturas.length){
      for(let i = 0; i < faturas.length; i++){
        let fatura = faturas[i];
        console.log('fatura executar cobrança: ' + fatura.codigo);
        let assinatura = await new MapeadorDeAssinatura().selecioneSync(fatura.contrato.assinatura.id);
        if(assinatura.cartao && assinatura.contrato.numeroParcelas){
          let resposta =    await new IuguService().executeCobrancaCartao(fatura,
                                assinatura.cartao.codigo, assinatura.contrato.numeroParcelas ).
                  catch( (erro) => {  console.log('Não foi possivel executar cobrança no iugu');   console.log(erro);     });

          if(resposta) {
            let invoice = await new IuguService().obtenhaFatura(fatura.codigo);
            await new ContratoService().sincronizeFaturaIugu(invoice);
          }
          await new MapeadorDeFatura().atualizeTentativaPagamento(fatura);
        } else {
          console.log('Assinatura não tem cartão associado: ' + assinatura.codigo);
        }
      }
    }
  }

  async reativeAssinatura(contrato: Contrato, novoVencimento: Date, operador: any) {
    let erro: string;
    if(!contrato.dataProximoVencimento || !moment(novoVencimento).isSame(moment(contrato.dataProximoVencimento), 'd'))
      erro = await this.altereDataVencimento(contrato, novoVencimento, operador);

    if(!erro){
      let subscription: any = await  new IuguService().ativeAssinatura(contrato.assinatura.codigo);

      await new RegistroDeOperacaoService(Ambiente.Instance.usuarioLogado(),
        Ambiente.Instance.ip()).ativouAssinatura(contrato.assinatura, contrato.empresa);

      await new ContratoService().sincronizeAssinaturaIugu( contrato, subscription);

      return null;
    } else{
     return erro;
    }
  }
}
