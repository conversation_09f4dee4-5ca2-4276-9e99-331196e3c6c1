import {MovimentacaoEstoqueInsumo} from "../domain/estoque/MovimentacaoEstoqueInsumo";
import {PedidoGenerico} from "../domain/delivery/PedidoGenerico";
import {MapeadorDeInsumo} from "../mapeadores/MapeadorDeInsumo";
import {MapeadorDeEstoque} from "../mapeadores/MapeadorDeEstoque";
import {Insumo} from "../domain/estoque/Insumo";
import {Estoque} from "../domain/estoque/Estoque";

import {Ambiente} from "./Ambiente";
import {FichaTecnica} from "../domain/estoque/FichaTecnica";
import {MotivoDaMovimentacao} from "../domain/estoque/MotivoDaMovimentacao";
import {IngredienteDaReceita} from "../domain/estoque/IngredienteDaReceita";
import {MapeadorDeMovimentacaoEstoqueInsumo} from "../mapeadores/MapeadorDeMovimentacaoEstoqueInsumo";
import {EnumStatusMovimentacao} from "../lib/EnumStatusMovimentacao";
import {EnumTipoFixaTecnica} from "../domain/estoque/EnumTipoFixaTecnica";
import {RegistroDeOperacaoService} from "./RegistroDeOperacaoService";

export class EstoqueService{
  registroDeOperacaoService: any;
  async registreMovimentacoesManual(dados: any, operador: any){
    return new Promise(async (resolve: any, reject: any) => {
      const   motivoDaMovimentacao: any = dados.motivoDaMovimentacao,
        observacao = dados.observacao, horario = new Date(dados.horario);

      let movimentacoes: any =  [];

      dados.insumos.forEach((itemInsumo: any) => {
        let movimentacao = new MovimentacaoEstoqueInsumo(itemInsumo.insumo, itemInsumo.quantidade)

        movimentacao.setTipoMovimentacaoManual( motivoDaMovimentacao, horario, operador, observacao);

        movimentacoes.push(movimentacao)
      })

      new MapeadorDeMovimentacaoEstoqueInsumo().transacao(async (conexao: any, commit: any) => {
        let erro: string;

        for(let i = 0; i < movimentacoes.length; i++){
          if(!erro) {
            let movimentacao: any =  movimentacoes[i];
            try{
              await this.salveMovimentacaoManual(movimentacao , operador);
            } catch (err){
              console.error(err)
              erro = err.message || err;
            }
          }
        }

        if(!erro){
          commit(() => { resolve( movimentacoes) })
        } else {
          conexao.rollback(() => {
            reject(erro)
          })
        }
      })
    })
  }

  private async salveMovimentacaoManual(movimentacao: MovimentacaoEstoqueInsumo, operador: any){
    await movimentacao.salve(true);

    if( movimentacao.insumo.estoque){
      movimentacao.insumo.estoque =  await new MapeadorDeEstoque().selecioneParaUpdate(movimentacao.insumo.estoque.id)

      if( movimentacao.insumo.estoque.quantidadeReservada < 0)
        movimentacao.insumo.estoque.quantidadeReservada = 0
      if( movimentacao.insumo.estoque.quantidade < 0)
        movimentacao.insumo.estoque.quantidade = 0

      movimentacao.insumo.estoque.quantidade +=  movimentacao.quantidade

      await new MapeadorDeEstoque().atualizeQuatidadesEstoque( movimentacao.insumo.estoque);
    }

    if(movimentacao.entradaPorBeneficiamento()){
      if(movimentacao.insumo.objeto !== 'ficha-tecnica')
        throw Error(`Insumo "${movimentacao.insumo.nome}" não é uma ficha tecnica `)

      let fichaTecnica: FichaTecnica = movimentacao.insumo as FichaTecnica

      if(fichaTecnica.tipo !== EnumTipoFixaTecnica.ProdutoBeneficiado)
        throw Error(`Insumo "${movimentacao.insumo.nome}" não é do tipo beneficiado `)


      let quantidadeSAida = Math.abs(movimentacao.quantidade) / fichaTecnica.receita.rendimento;
      await this.deBaixaNasComposicoes(fichaTecnica, quantidadeSAida, null, movimentacao.insumo, operador)
    }
  }

  async registreMovimentacaoManual(dados: any,  operador: any){
    return new Promise(async (resolve: any, reject: any) => {
      let quantidade = dados.quantidade;
      let movimentacao = new MovimentacaoEstoqueInsumo(dados.insumo, quantidade)

      movimentacao.setTipoMovimentacaoManual( dados.motivoDaMovimentacao, new Date(dados.horario), operador, dados.observacao);

      new MapeadorDeMovimentacaoEstoqueInsumo().transacao(async (conexao: any, commit: any) => {
        try{
          await this.salveMovimentacaoManual(movimentacao , operador);

          commit(() => { resolve(movimentacao) })
        } catch (err){
           console.error(err)
             conexao.rollback(() => {
              reject(err.message || err)
            })
        }
      })
    })


  }

  async registreMovimentacaoEstoqueAtual(insumo: any, quantidade: number, operador: any) {
    let movimentacao = new MovimentacaoEstoqueInsumo(insumo, quantidade)

    movimentacao.setTipoMovimentacaoManual(MotivoDaMovimentacao.EstoqueAtual, new Date(), operador);

    await movimentacao.salve(true)

  }

  async registreMovimentacaoManualDoProduto(produto: any,  quantidade: number, motivo: any, observacao: string, operador: any) {
    let movimentacao = new MovimentacaoEstoqueInsumo(null, quantidade)

    movimentacao.produto = produto
    movimentacao.estoque = produto.estoque;
    if(produto.estoque.quantidade < produto.estoque.quantidadeReservada)
      throw Error(`${produto.estoque.quantidadeReservada} produtos estão reservados, estoque mínimo permetido é ${produto.estoque.quantidadeReservada}`)

    movimentacao.setTipoMovimentacaoManual(motivo, new Date(), operador, observacao);

    await movimentacao.salve(true)
  }

  async deBaixaEstoqueInsumos(pedido: PedidoGenerico) {
    let movimentacoes: Array<MovimentacaoEstoqueInsumo> = pedido.obtenhaMovimentacoesEstoque(pedido.empresa.estoqueVinculadoProduto);

    if(!pedido.empresa.estoqueVinculadoProduto){
      let mapInsumosAtualizar: any = {};

      for(let i = 0; i < movimentacoes.length; i++){
        let insumo: any  = movimentacoes[i].insumo;

        insumo.empresa = pedido.empresa;

        if(!mapInsumosAtualizar[insumo.id])
          mapInsumosAtualizar[insumo.id] = { insumo: insumo, quantidade: 0};

        mapInsumosAtualizar[insumo.id].quantidade += Math.abs(movimentacoes[i].quantidade);

        await movimentacoes[i].salve(true);
      }

      for(let key of Object.keys(mapInsumosAtualizar)){
        let dadosInsumo: any   = mapInsumosAtualizar[key];

        if(dadosInsumo.insumo.objeto === 'ficha-tecnica'){
          await this.deBaixaNasComposicoes(dadosInsumo.insumo, dadosInsumo.quantidade, pedido)
        } else {
          await this.reserveEstoqueInsumo(dadosInsumo.insumo, dadosInsumo.quantidade)
        }
      }
    } else {
      let mapProdutosAtualizar: any = {};

      for(let i = 0; i < movimentacoes.length; i++){
        let produto: any  = movimentacoes[i].produto;

        produto.empresa = pedido.empresa;

        if(!mapProdutosAtualizar[produto.id])
          mapProdutosAtualizar[produto.id] = { produto: produto, quantidade: 0};

        mapProdutosAtualizar[produto.id].quantidade += Math.abs(movimentacoes[i].quantidade);

        await movimentacoes[i].salve(true);
      }

      for(let key of Object.keys(mapProdutosAtualizar)){
        let dadosProduto: any   = mapProdutosAtualizar[key];
        await this.atualizeSaidaEstoqueProduto(dadosProduto.produto, dadosProduto.quantidade)
      }
    }
  }

  async deBaixaNasComposicoes(insumo: FichaTecnica, quantidade: number, pedido: any,
                              insumoBeneficado: any = null, operador: any = null) {
    ///pegar insumo com ingredientes
    let insumoDoBanco: FichaTecnica = await new MapeadorDeInsumo().selecioneSync(insumo.id);

  //  if(!insumoDoBanco.fichaTecnicaDeProdutoBeneficado())
    //  throw Error(`Insumo "${insumoDoBanco.nome}" não é uma ficha tecnica do tipo beneficiado`)

    let mapeador =   new MapeadorDeEstoque();

    for(let i = 0; i < insumoDoBanco.receita.ingredientes.length; i++){
      let ingredienteNaReceita: IngredienteDaReceita = insumoDoBanco.receita.ingredientes[i];
      let insumoDoIngrediente: Insumo = ingredienteNaReceita.insumo;
      let quantidadeBaixa: number = quantidade * ingredienteNaReceita.quantidade;

      if(!insumoDoIngrediente.estoque)
        throw Error(`Insumo ${insumoDoIngrediente.nome} nao tem estoque cadastrada` )

      let estoqueDoIngrediente: Estoque = await mapeador.selecioneParaUpdate(insumoDoIngrediente.estoque.id)

      if(!estoqueDoIngrediente.temDisponivel(quantidadeBaixa))
        throw Error(`Estoque insuficiente (${quantidade}/${estoqueDoIngrediente.getQuantidade()}) de "${insumoDoIngrediente.nome}"` )

      //todo: possilvmente criar um flag insumo indicar se retorna estoque depois de produzido
      //aqui sera retornado se for um ficha tecnca beneficiada
      let retornarEstoque = insumoDoIngrediente.fichaTecnicaDeProdutoBeneficado();

      let movimentacao = new MovimentacaoEstoqueInsumo(insumoDoIngrediente, quantidadeBaixa, retornarEstoque);

      if(insumoBeneficado){
        movimentacao.operador = operador;
        movimentacao.setTipoSaidaPorBeneficamento(insumoBeneficado);
        estoqueDoIngrediente.quantidade -=   quantidadeBaixa;
      } else if(pedido){
        movimentacao.setTipoSaidaProdutoDoPedido(null, pedido, insumoDoBanco);
        estoqueDoIngrediente.quantidadeReservada +=   quantidadeBaixa;
      }

      await mapeador.atualizeQuatidadesEstoque(estoqueDoIngrediente);

      await movimentacao.salve(true);
    }
  }

  async confirmeReserva(movimentacao: MovimentacaoEstoqueInsumo){
    if(movimentacao.estoque){
      let mapeador =   new MapeadorDeEstoque();
      let estoque: Estoque = await mapeador.selecioneParaUpdate(movimentacao.estoque.id)

      estoque.quantidadeReservada -= Math.abs(movimentacao.quantidade);
      estoque.quantidade -= Math.abs(movimentacao.quantidade);

      await mapeador.atualizeQuatidadesEstoque(estoque);
    }

    await movimentacao.confirmouReserva();


  }
  async reserveEstoqueInsumo(insumo: any, quantidade: number) {
    let mapeador =   new MapeadorDeEstoque();

    if(!insumo.estoque) throw Error(`insumo sem estoque vinculado: ${insumo.id}  - ${insumo.nome}`);

    //garantir que nunca faça leitura suja
    let estoque: Estoque = await mapeador.selecioneParaUpdate(insumo.estoque.id)
    if(estoque.temDisponivel(quantidade)){
      estoque.quantidadeReservada += Math.abs(quantidade);
      await mapeador.atualizeQuantidadeReservada(estoque);
      insumo.estoque = estoque;
      //atualizando insumo na cache
      await mapeador.insiraNaCachePorid(estoque);
    } else {
      throw Error(`Estoque insuficiente (${quantidade}/${estoque.getQuantidade()}) de "${insumo.nome}" retire do carrinho` )
    }
  }

  async atualizeSaidaEstoqueProduto(produto: any, quantidade: number) {
    let mepeador =   new MapeadorDeEstoque();
    //garantir que nunca faça leitura suja
    let estoque: Estoque = await mepeador.selecioneParaUpdate(produto.estoque.id)
    if(estoque.temDisponivel(quantidade)){
      estoque.quantidadeReservada += Math.abs(quantidade);
      await mepeador.atualizeQuantidadeReservada(estoque);
    } else {
      throw Error(`Estoque insuficiente (${quantidade}/${estoque.getQuantidade()}) de "${produto.nome}" retire do carrinho` )
    }
  }

  salveInsumo(dados: any, operador: any) {
    let erroSalvar: string, insumo: Insumo;
    return new Promise<any>( async (resolve, reject) => {
      insumo = !dados.fichaTecnica ? Object.assign( new Insumo( ) , dados) :  FichaTecnica.nova(dados);

      insumo.estoque = Object.assign(new Estoque( ), dados.estoque );

      await insumo.valide().catch((erro: any) => {
          erroSalvar = erro;
      });

      if(erroSalvar) return reject(erroSalvar)

      new MapeadorDeInsumo().transacao(async (conexao: any, commit: any) => {
        await insumo.insira()

        await this.obtenhaRegistroDeOperacaoService().criouInsumo(insumo)

        if(insumo.estoque && insumo.estoque.quantidade > 0)
          await new EstoqueService().registreMovimentacaoEstoqueAtual(insumo,   insumo.estoque.quantidade, operador);

        commit(() => {
          resolve(insumo)
        })
      })
    })

  }

  atualizeInsumo(dados: any, operador: any){
    return new Promise<any>( async (resolve, reject) => {
      let insumo: Insumo  = await new MapeadorDeInsumo().selecioneSync(dados.id);
      if(insumo instanceof FichaTecnica){
        FichaTecnica.getDados(insumo, dados)
      } else {
        Object.assign(insumo, dados );
      }

      insumo.empresa =  Ambiente.Instance.empresaContexto();
      let erroSalvar: string

      await insumo.valide().catch((erro: any) => {
        erroSalvar = erro;
      });

      if(erroSalvar) return reject(erroSalvar)

      new MapeadorDeInsumo().transacao(async (conexao: any, commit: any)  => {
        await insumo.atualize();
        await this.obtenhaRegistroDeOperacaoService().editouInsumo(insumo)
        commit(() => {
          resolve(insumo)
        })
      })
    });
  }

  async desativeEstoqueDoProduto(produto: any, estoque: any, operador: any){
    let mapeador = new MapeadorDeEstoque();

    estoque.ativo = false;

    await mapeador.atualizeEstoqueAtivo(estoque);

    await this.obtenhaRegistroDeOperacaoService().atualizouEstoqueAtivo(produto, produto.estoque,
      `Estoque do produto "${produto.nome}" desativado`);
  }

  async salveEstoqueDoProduto(produto: any, dadosEstoque: any, operador: any) {
    produto.estoque = Object.assign(new Estoque(), produto.estoque);

    let mapeador = new MapeadorDeEstoque();

    if(!dadosEstoque.id){
      await mapeador.insiraGraph(produto.estoque)
      await this.registreMovimentacaoManualDoProduto(produto, produto.estoque.quantidade,
        MotivoDaMovimentacao.EstoqueAtual, `Estoque atual definido no produto "${produto.nome}"`,  operador);
    } else{
      let estoqueBanco  = await mapeador.selecioneParaUpdate(dadosEstoque.id);

      if(!estoqueBanco.ativo){
        estoqueBanco.ativo  = true;
        await mapeador.atualizeEstoqueAtivo(estoqueBanco);

        await this.obtenhaRegistroDeOperacaoService().atualizouEstoqueAtivo(produto, produto.estoque,
          `Estoque do produto "${produto.nome}" ativado`);
      }

      let qtdeAtualizada = dadosEstoque.quantidade - estoqueBanco.quantidade;

      if(qtdeAtualizada){
        await mapeador.atualizeQuatidadeEstoque(dadosEstoque)
        const motivo = qtdeAtualizada > 0 ? MotivoDaMovimentacao.EntradaManual : MotivoDaMovimentacao.SaidaManual;

        await  this.registreMovimentacaoManualDoProduto(produto,  qtdeAtualizada, motivo,
          `Estoque atualizado no produto "${produto.nome}"`, operador);
      }


      if(dadosEstoque.quantidadeMinima !== estoqueBanco.quantidadeMinima)
        await mapeador.atualizeSync(dadosEstoque)

    }

    await mapeador.atualizeEstoqueProduto(produto)
  }

  async confirmeMovimentacoesReservadas(pedido: PedidoGenerico) {
    const movimentacoes: Array<MovimentacaoEstoqueInsumo> = await new MapeadorDeMovimentacaoEstoqueInsumo().listeAsync({
      idPedido: pedido.id,
      status: EnumStatusMovimentacao.Reservada
    });

    // Encontrar movimentações de ficha técnica e de insumos normais
    const movimentacaoDeFicha = movimentacoes.find((item) => item.insumo instanceof FichaTecnica);
    const movimentacaoDeInsumo = movimentacoes.find((item) => !item.insumo || item.insumo instanceof Insumo);

    // Definir se a reserva deve ser confirmada
    const confirmarReserva = this.deveConfirmarReserva(movimentacaoDeFicha, movimentacaoDeInsumo, pedido);

    // Se a reserva deve ser confirmada, processar todas as movimentações
    if (confirmarReserva) {
      await Promise.all(movimentacoes.map(async (movimentacao) => {
        await this.confirmeReserva(movimentacao);
      }));
    }
  }

  private deveConfirmarReserva(movimentacaoDeFicha: MovimentacaoEstoqueInsumo  = null,
                               movimentacaoDeInsumo: MovimentacaoEstoqueInsumo = null,
                               pedido: PedidoGenerico): boolean {
    if (movimentacaoDeFicha)
      return movimentacaoDeFicha.insumo.quandoConfirmar(pedido.status);

    // Caso não tenha movimentação de ficha, verifica a de insumo
    return !movimentacaoDeInsumo || movimentacaoDeInsumo.insumo.quandoConfirmar(pedido.status);
  }

  async canceleMovimentacoesPedido(pedido: PedidoGenerico, operador: any) {
    return new Promise<any>( async (resolve, reject) => {
      let movimentacoes: Array<MovimentacaoEstoqueInsumo>  =
        await new MapeadorDeMovimentacaoEstoqueInsumo().listeAsync({idPedido: pedido.id});

      let reservadas: any = movimentacoes.filter((item: any) => item.estaReservado());

      let confirmadas: any = movimentacoes.filter((item: any) => item.foiConfirmada());

      for(let i = 0; i < reservadas.length; i++){

        let movimentacao: MovimentacaoEstoqueInsumo  = reservadas[i];

        await movimentacao.processeCancelamento();
      }

      for(let i = 0; i < confirmadas.length; i++){
        let movimentacao: MovimentacaoEstoqueInsumo  = confirmadas[i];

        await movimentacao.processeEstorno(pedido, operador);
      }

      resolve(null);
    });
  }

  async vinculeInsumoAosProdutos(insumo: any, produtos: any){
    return new Promise<any>(  (resolve, reject) => {

      new MapeadorDeInsumo().transacao(async (onexao: any, commit: any) => {
        await this.registreVinculoInsumoAosProdutos(insumo, produtos)
        commit( () => {
          resolve(null);
        })
      })
    })
  }

  async registreVinculoInsumoAosProdutos(insumo: any, produtos: Array<any>){
    await new MapeadorDeInsumo().insiraNaListaProdutos(insumo, produtos);

    await this.obtenhaRegistroDeOperacaoService().vinculouInsumoProduto(insumo, produtos);

  }

  async registreVinculoInsumoAsOpcoes(insumo: any, opcoes: any){
    await new MapeadorDeInsumo().insiraNaListaOpcoes(insumo, opcoes);

    await this.obtenhaRegistroDeOperacaoService().vinculouInsumoOpcao(insumo, opcoes);
  }


  async removaVinculoInsumoAoProduto(insumo: any, produto: any) {

    return new Promise<any>(  (resolve, reject) => {

      new MapeadorDeInsumo().transacao(async (onexao: any, commit: any) => {
        await new MapeadorDeInsumo().removaNaListaProdutos(insumo, produto);

        await this.obtenhaRegistroDeOperacaoService().removeuVinculoInsumoProduto(insumo, produto);

        commit( () => {
          resolve(null);
        })

      })
    })

  }

  async removaVinculoInsumoAOpcao(insumo: any, opcao: any) {
    return new Promise<any>(  (resolve, reject) => {

      new MapeadorDeInsumo().transacao(async (onexao: any, commit: any) => {
        await new MapeadorDeInsumo().removaNaListaOpcao(insumo, opcao);

        await this.obtenhaRegistroDeOperacaoService().removeuVinculoInsumoOpcao(insumo, opcao);

        commit( () => {
          resolve(null);
        })

      })
    })


  }

  async vinculeInsumoAsOpcoes(insumo: any, opcoes: any) {
    return new Promise<any>(  (resolve, reject) => {

      new MapeadorDeInsumo().transacao(async (onexao: any, commit: any) => {
        await this.registreVinculoInsumoAsOpcoes(insumo, opcoes);
        commit( () => {
          resolve(null);
        })

      })
    })
  }



  obtenhaRegistroDeOperacaoService(): RegistroDeOperacaoService {
    if(!this.registroDeOperacaoService) this.registroDeOperacaoService = new RegistroDeOperacaoService(
      Ambiente.Instance.usuarioLogado(), Ambiente.Instance.ip()
    )

    return this.registroDeOperacaoService
  }



}
