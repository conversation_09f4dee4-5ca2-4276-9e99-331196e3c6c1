import {<PERSON><PERSON><PERSON>} from "../domain/Usuario";
import {Produto} from "../domain/Produto";
import {RegistroDeOperacao} from "../domain/auditoria/RegistroDeOperacao";
import {EnumOperacao} from "../domain/auditoria/EnumOperacao";
import {MapeadorDeRegistroDeOperacao} from "../mapeadores/MapeadorDeRegistroDeOperacao";
import {AdicionalDeProduto} from "../domain/delivery/AdicionalDeProduto";
import {AdicionalDeProdutoMultiplaEscolha} from "../domain/delivery/AdicionalDeProdutoMultiplaEscolha";
import {EnumDisponibilidadeProduto} from "../lib/emun/EnumDisponibilidadeProduto";
import {Categoria} from "../domain/delivery/Categoria";
import {FormaDeEntregaEmpresa} from "../domain/delivery/FormaDeEntregaEmpresa";
import {EnumTipoDeCobranca} from "../domain/delivery/EnumTipoDeCobranca";
import {HorarioFuncionamento} from "../domain/HorarioFuncionamento";
import {DiaDaSemanaEnum} from "../domain/DiaDaSemanaEnum";
import {Cupom} from "../domain/faturamento/Cupom";
import {Contrato} from "../domain/faturamento/Contrato";
import {Assinatura} from "../domain/faturamento/Assinatura";
import {Empresa} from "../domain/Empresa";
import {OpcaoDeAdicionalDeProduto} from "../domain/delivery/OpcaoDeAdicionalDeProduto";
import {Promocao} from "../domain/Promocao";
import {Plano} from "../domain/Plano";
import {Brinde} from "../domain/obj/Brinde";
import {Atividade} from "../domain/Atividade";
import {EnumStatusMia} from "../domain/chatbot/EnumStatusMia";
import {FormaDePagamento} from "../domain/delivery/FormaDePagamento";
import {Insumo} from "../domain/estoque/Insumo";
import { Tablet } from "../domain/Tablet";
import { Mesa } from "../domain/Mesa";


export class RegistroDeOperacaoService {

  constructor(public usuario: Usuario, public ip: string,
              public clienteApi: any = null) {
  }

  private insiraRegistroDeOperacao(descricao: string, operacao: EnumOperacao,
                                   idObjeto: any = null, tipoObjeto: string = null, valorNovo: any = null, empresa: any = null) {

    let registroDeOperacao = new RegistroDeOperacao(
      descricao.substr(0, 255),
      operacao,
      new Date(),
      this.usuario,
      this.clienteApi,
      this.ip,
      idObjeto,
      tipoObjeto,
      JSON.stringify(valorNovo)
    )

    if(empresa)
      registroDeOperacao.empresa = empresa

    return (new MapeadorDeRegistroDeOperacao()).insiraGraph(registroDeOperacao)
  }


  sincronizouPrecoProduto(produto: any){
    return new Promise<void>(resolve => {
      this.insiraRegistroDeOperacao('Sincronizou preço produto ' + produto.nome,
        EnumOperacao.EditarProduto,  produto.id,
        Produto.name, { preco: produto.preco}).then(() => {
        resolve();
      })
     })
  }

  sincronizouFotoProduto(produto: any){
    return new Promise<void>(resolve => {
      this.insiraRegistroDeOperacao('Sincronizou imagem produto ' + produto.nome,
        EnumOperacao.EditarProduto,  produto.id,
        Produto.name, { foto: produto.imagens[0].linkImagem}).then(() => {
        resolve();
      })
    })
  }

  sincronizouDescricaoProduto(produto: any){
    return new Promise<void>(resolve => {
      this.insiraRegistroDeOperacao('Sincronizou descriçao produto ' + produto.nome,
        EnumOperacao.EditarProduto,  produto.id,
        Produto.name, { descricao: produto.descricao}).then(() => {
        resolve();
      })
     })
  }

  editouProduto(produto: Produto) {
     return new Promise<void>(resolve => {
       this.insiraRegistroDeOperacao('Editou o produto ' + produto.nome,
         EnumOperacao.EditarProduto,
         produto.id,
         Produto.name,
         {
           id: produto.id,
           nome: produto.nome,
           descricao: produto.descricao,
           codigoPdv: produto.codigoPdv,
           tipoDeVenda: produto.tipoDeVenda,
           unidade: produto.unidadeMedida ? produto.unidadeMedida.nome : null,
           valorInicial: produto.valorInicial,
           incremento: produto.incremento,
           preco: produto.preco,
           categoria: produto.categoria ? produto.categoria.nome : null,
           desconto: produto.percentualDesconto,
           qtdMinima:   produto.qtdeMinima,
           qtdMaxima: produto.qtdMaxima,
           pesoMinimo: produto.pesoMinimo,
           pesoMaximo: produto.pesoMaximo,
          }).then(() => {
         resolve();
       })
     })
  }

  criouNovoProduto( produto: Produto) {
    return new Promise<void>(resolve => {
      this.insiraRegistroDeOperacao('Criou o produto ' + produto.nome,
        EnumOperacao.CriarProduto,
        produto.id,
        Produto.name,
        {
          id: produto.id,
          nome: produto.nome,
          descricao: produto.descricao,
          codigoPdv: produto.codigoPdv,
          tipoDeVenda: produto.tipoDeVenda,
          unidade: produto.unidadeMedida ? produto.unidadeMedida.nome : null,
          valorInicial: produto.valorInicial,
          incremento: produto.incremento,
          preco: produto.preco,
          categoria: produto.categoria ? produto.categoria.nome : null,
          desconto: produto.percentualDesconto,
          qtdMinima:   produto.qtdeMinima,
          qtdMaxima: produto.qtdMaxima,
          pesoMinimo: produto.pesoMinimo,
          pesoMaximo: produto.pesoMaximo,
          //As opções abaixo são sempre verdadeiras assim que cadsatra o produto
          disponbilidade: 'SempreDisponivel',
          cardapioMesa: true,
          cardapioDelivery: true
        }).then(() => {
        resolve();
      })
    })
  }



  inseriuAdicionalNoProduto(produto: Produto, adicional: AdicionalDeProduto, copiou = false) {
    return new Promise<void>(resolve => {
      let dtoAdicional: any = {
        id: adicional.id,
        nome: adicional.nome,
        obrigatorio: adicional.obrigatorio,
        tipo: adicional.tipo
      }

      if(adicional.tipo === 'multipla-escolha') {
        let adicionalMultiplaEscolha: AdicionalDeProdutoMultiplaEscolha = adicional as AdicionalDeProdutoMultiplaEscolha

        dtoAdicional.tipo = 'Múltipla Escolha'
        dtoAdicional.qtdMinima = adicionalMultiplaEscolha.qtdMinima
        dtoAdicional.qtdMaxima = adicionalMultiplaEscolha.qtdMaxima
        dtoAdicional.podeRepetirItens = adicionalMultiplaEscolha.podeRepetirItem
        dtoAdicional.tipoCobranca = adicionalMultiplaEscolha.tipoDeCobranca
      }

      dtoAdicional.opcoes = adicional.opcoesDisponiveis.map(opcao => ({ nome: opcao.nome, descricao: opcao.descricao,  valor: opcao.valor,
        disponivel: opcao.disponivel, possuiImagem: opcao.linkImagem != null  }) )

      this.insiraRegistroDeOperacao(
        `${copiou ? 'Copiou' : 'Inseriu'} o adicional ${adicional.nome} no produto ${produto.nome}`,
        EnumOperacao.InseriuAdicionalDeProduto,
        produto.id,
        Produto.name,
        dtoAdicional

      ).then(() => {
        resolve()
      })
    })
  }

  removeuAdicionalProduto(produto: any, adicional: any, opcaoAtualizada: any){
    return new Promise<void>(resolve => {
      let dtoAdicional: any = {
        id: adicional.id,
        nome: adicional.nome,
        obrigatorio: adicional.obrigatorio,
        tipo: adicional.tipo,
        opcoes: [
          {nome: opcaoAtualizada.nome, valor: opcaoAtualizada.valor}
        ]
      }

      this.insiraRegistroDeOperacao(
        'Sincronizou, removeu opçao: ' + opcaoAtualizada.nome + (adicional && adicional.nome ?  ' no adicional ' + adicional.nome : ''),
        EnumOperacao.AlterouAdicionalDeProduto,
        produto && produto.id ? produto.id : null,
        produto && produto.id ? Produto.name : null,
        dtoAdicional

      ).then(() => {
        resolve()
      })
    })
  }

  alterouAceitePedidoAutomatico(empresa: any ){
    return new Promise<void>(resolve => {
      this.insiraRegistroDeOperacao(
        'Alterou aceitar pedido automatico para ' +  empresa.aceitarPedidoAutomatico ,
        EnumOperacao.AlterouAceitarPedido,
        empresa.id,
        Empresa.name,
        {aceitarPedidoAutomatico:  empresa.aceitarPedidoAutomatico }

      ).then(() => {
        resolve()
      })
    })
  }


  alterouPrecoAdicionalProduto(produto: any, adicional: any, opcaoAtualizada: any){
    return new Promise<void>(resolve => {
      let dtoAdicional: any = {
        id: adicional.id,
        nome: adicional.nome,
        obrigatorio: adicional.obrigatorio,
        tipo: adicional.tipo,
        opcoes: [
          {nome: opcaoAtualizada.nome, valor: opcaoAtualizada.valor}
        ]
      }

      this.insiraRegistroDeOperacao(
        'Sincronizou preço do adicional ' + opcaoAtualizada.nome +
        (adicional && adicional.nome ?  ' no adicional ' + adicional.nome : ''),
        EnumOperacao.AlterouAdicionalDeProduto,
        produto && produto.id ? produto.id : null,
        produto && produto.id ? Produto.name : null,
        dtoAdicional

      ).then(() => {
        resolve()
      })
    })
  }



  alterouDescricaoAdicionalProduto(produto: any, adicional: any, opcaoAtualizada: any){
    return new Promise<void>(resolve => {
      let dtoAdicional: any = {
        id: adicional.id,
        nome: adicional.nome,
        obrigatorio: adicional.obrigatorio,
        tipo: adicional.tipo,
        opcoes: [
          {nome: opcaoAtualizada.nome, descricao: opcaoAtualizada.descricao}
        ]
      }

      this.insiraRegistroDeOperacao(
        'Sincronizou descricao do adicional ' + opcaoAtualizada.nome +
        (adicional && adicional.nome ?  ' no adicional ' + adicional.nome : ''),
        EnumOperacao.AlterouAdicionalDeProduto,
        produto && produto.id ? produto.id : null,
        produto && produto.id ? Produto.name : null,
        dtoAdicional

      ).then(() => {
        resolve()
      })
    })
  }

  alterouOpcaoDoAdicionalProduto(produto: Produto, adicional: any, opcaoAtualizada: any, opcaoInserida: any, opcaoRemovida: any){
    return new Promise<void>(resolve => {
      let dtoAdicional: any = {
        id: adicional.id,
        nome: adicional.nome
      }
      let descricao = '';

      if(opcaoRemovida ){
        dtoAdicional.opcaoRemovida = opcaoRemovida
        descricao = String(`Removou opção "${opcaoRemovida.nome}"`)
        delete dtoAdicional.opcaoRemovida.adicionalProduto;
      } else{
        dtoAdicional.opcao  = opcaoInserida || opcaoAtualizada;
        const operacao = (opcaoInserida ? 'Inseriu' : 'Atualizou');
        descricao =  String(`${operacao}  opção  "${dtoAdicional.opcao.nome}"`)

        delete dtoAdicional.opcao.adicionalProduto;
      }

      descricao +=   String(` no adicional "${adicional.nome}"`) +
        (produto && produto.id ?  ' do produto ' + produto.id : '');


      this.insiraRegistroDeOperacao(
        descricao,
        EnumOperacao.AlterouAdicionalDeProduto,
        produto && produto.id ? produto.id : null,
        produto && produto.id ? Produto.name : null,
        dtoAdicional

      ).then(() => {
        resolve()
      })

    })
  }

  alterouAdicionalDeProduto(produto: Produto, adicional: any,  opcoesAtualizadas: any = [], opcoesRemovidas: any[],
                            disponibilidadePorEmpresa: boolean = null) {
    return new Promise<void>(resolve => {
      let dtoAdicional: any = {
        id: adicional.id,
        nome: adicional.nome,
        obrigatorio: adicional.obrigatorio,
        tipo: adicional.tipo
      }

      if(adicional.tipo === 'multipla-escolha') {
        let adicionalMultiplaEscolha: AdicionalDeProdutoMultiplaEscolha = adicional as AdicionalDeProdutoMultiplaEscolha

        dtoAdicional.tipo = 'Múltipla Escolha'
        dtoAdicional.qtdMinima = adicionalMultiplaEscolha.qtdMinima
        dtoAdicional.qtdMaxima = adicionalMultiplaEscolha.qtdMaxima
        dtoAdicional.podeRepetirItens = adicionalMultiplaEscolha.podeRepetirItem
        dtoAdicional.tipoCobranca = adicionalMultiplaEscolha.tipoDeCobranca
      }

      if(opcoesRemovidas.length){
        dtoAdicional.opcoesRemovidas = opcoesRemovidas.map(opcao => ({ nome: opcao.nome, descricao: opcao.descricao,  valor: opcao.valor,
          disponivel: opcao.disponivel, possuiImagem: opcao.linkImagem != null  }) )
      }

      if(opcoesAtualizadas.length)
        dtoAdicional.opcoesAtualizadas = opcoesAtualizadas.map((opcao: any) =>  {
            let atualizacao: any = { id: opcao.id, nome: opcao.nome,   disponivel: opcao.disponivel  }

           if(disponibilidadePorEmpresa && opcao.opcaoNaEmpresa )
              atualizacao.disponivelNaEmpresa = opcao.opcaoNaEmpresa.disponivel

           return atualizacao;
        })

       this.insiraRegistroDeOperacao(
        'Alterou o adicional ' + adicional.nome + (produto && produto.id ?  ' no produto ' + produto.id : ''),
        EnumOperacao.AlterouAdicionalDeProduto,
        produto && produto.id ? produto.id : null,
        produto && produto.id ? Produto.name : null,
        dtoAdicional

      ).then(() => {
        resolve()
      })
    })
  }

  atualizouDisponibilidadeCategoria(categoria: any) {
      return new Promise<void>(resolve => this.insiraRegistroDeOperacao(
        (categoria.disponivel ? 'Ativou ' : 'Parou ') + 'as vendas da categoria ' + categoria.nome,
        categoria.disponivel ? EnumOperacao.AtivouCategoria : EnumOperacao.DesativouCategoria,
        categoria.id,
        Categoria.name
      ).then(() => resolve()))
  }

  removeuProduto(produto: any, descricao: string = null) {

      return new Promise<void>(resolve => this.insiraRegistroDeOperacao(
        (descricao ? descricao : ('Removeu o produto ' + produto.id)),
        EnumOperacao.RemoveuProduto,
        produto.id,
        Produto.name
      ).then(() => resolve()))
  }

  removeuPromocao(promocao: any) {
    return new Promise<void>(resolve => this.insiraRegistroDeOperacao(
      'Removeu a promoção ' + promocao.id,
      EnumOperacao.RemoveuPromocao,
      promocao.id,
      Promocao.name
    ).then(() => resolve()))
  }

  criouInsumo( insumo: Insumo) {
    let dados: any = Object.assign({}, insumo)

    delete dados.empresa;
    delete dados.produtos;
    delete dados.opcoes;
    return new Promise<void>(resolve => {
      this.insiraRegistroDeOperacao('Criou o insumo ' + insumo.nome,
        EnumOperacao.CriarInsumo,
        insumo.id,
        Insumo.name,   dados).then(() => {
        resolve();
      })
    })
  }

  editouInsumo( insumo: Insumo) {
    let dados: any = Object.assign({}, insumo)

    delete dados.empresa;
    delete dados.produtos;
    delete dados.opcoes;
    delete dados.estoque;
    delete dados.receita;


    return new Promise<void>(resolve => {
      this.insiraRegistroDeOperacao('Editou o insumo ' + insumo.nome,
        EnumOperacao.EditarInsumo,
        insumo.id,
        Insumo.name, dados).then(() => {
        resolve();
      })
    })
  }

  async vinculouInsumoProduto(insumo: any, produtos: any) {
    let valorNovo: any = { produtos: produtos.map((item: any) => ({ id: item.id, nome: item.nome, codigoPdv: item.codigoPdv}) ) };

    return new Promise<void>(resolve => {
      this.insiraRegistroDeOperacao(`Vinculou o insumo  aos produtos`,
        EnumOperacao.EditarInsumo,
        insumo.id,
        Insumo.name, valorNovo).then(() => {
        resolve();
      })
    })
  }

  async vinculouInsumoOpcao(insumo: any, opcoes: any) {
    let valorNovo: any = { opcoes: opcoes.map((item: any) => ({ id: item.id, nome: item.nome, codigoPdv: item.codigoPdv}) ) };

    return new Promise<void>(resolve => {
      this.insiraRegistroDeOperacao(`Vinculou o insumo aos adicionais`,
        EnumOperacao.EditarInsumo,
        insumo.id,
        Insumo.name, valorNovo).then(() => {
        resolve();
      })
    })
  }

  async removeuVinculoInsumoProduto(insumo: any, produto: any) {
    return new Promise<void>(resolve => {
      this.insiraRegistroDeOperacao(`Removeu vinculo do insumo  ao produto`,
        EnumOperacao.EditarInsumo,
        insumo.id,
        Insumo.name, { produto: {id: produto.id, nome: produto.nome, codigo: produto.codigo}}).then(() => {
        resolve();
      })
    })
  }

  async removeuVinculoInsumoOpcao(insumo: any, opcao: any) {
    return new Promise<void>(resolve => {
      this.insiraRegistroDeOperacao(`Removeu vinculo do insumo  ao adicional`,
        EnumOperacao.EditarInsumo,
        insumo.id,
        Insumo.name, { opcao: {id: opcao.id, nome: opcao.nome, codigo: opcao.codigo}}).then(() => {
        resolve();
      })
    })
  }


  removeuInsumo( insumo: Insumo) {
    return new Promise<void>(resolve => this.insiraRegistroDeOperacao(
      'Removeu insumo ' + insumo.id,
      EnumOperacao.RemoveuInsumo,
      insumo.id,
      Insumo.name
    ).then(() => resolve()))
  }

  removeuAdicionalDeProduto(produto: Produto, adicional: any) {
    return new Promise<void>(resolve => {
      let dtoAdicional: any = {
        id: adicional.id,
        nome: adicional.nome,
        tipo: adicional.tipo
      }

      this.insiraRegistroDeOperacao(
        'Removeu o adicional ' + adicional.nome + ' no produto ' + produto.id,
        EnumOperacao.RemoveuAdicionalDeProduto,
        produto.id,
        Produto.name,
        dtoAdicional

      ).then(() => {
        resolve()
      })
    })
  }

  reordenouAdicionaisDoProduto(produto: any) {
      return new Promise<void>(resolve => {
        this.insiraRegistroDeOperacao(
          'Reordenou os adicionais do produto ' + produto.id,
          EnumOperacao.ReordenouAdicionaisDoProduto,
          produto.id,
          Produto.name,
          produto.adicionais
        )
      })
  }

  reordenouProdutosEmpresa(empresa: Empresa, pornome: boolean = false){
    let descricao  = 'Reordenou produdos ';
    if(pornome)
       descricao = String(`${descricao} (por nome)`)

    this.insiraRegistroDeOperacao(
      descricao,
      EnumOperacao.ReordenouProdutos,
      empresa.id,
      Empresa.name,
      {}
    )
  }
  alterouDisponibilidadeOpcaoAdicional(dadosDisponibilidade: any, descricaoAlteracao: string = 'Alterou a disponibilidade do adicional '){
    return new Promise<void>((resolve, reject) => {
      let dtoDisponibilidade: any = {
        disponibilidade: dadosDisponibilidade.disponivel ? 'Disponivel' : "Indisponível"
      }

      this.insiraRegistroDeOperacao(String(`${descricaoAlteracao} "${dadosDisponibilidade.nome}"`) ,
        EnumOperacao.MudouDisponibilidadeOpcaoAdicional,
        dadosDisponibilidade.id,
        OpcaoDeAdicionalDeProduto.name,
        dtoDisponibilidade
      ).then(() => {
        resolve()
      })

    })
  }

  alterouDisponibilidadeOpcaoAdicionalDaEmpresa(dadosDisponibilidade: any, descricaoAlteracao: string = 'Alterou a disponibilidade do adicional '){
    return new Promise<void>((resolve, reject) => {
      let dtoDisponibilidade: any = {
        disponibilidade: dadosDisponibilidade.opcaoNaEmpresa.disponivel ? 'Disponivel' : "Indisponível"
      }

      this.insiraRegistroDeOperacao(String(`${descricaoAlteracao} "${dadosDisponibilidade.nome}"`) ,
        EnumOperacao.MudouDisponibilidadeOpcaoAdicional,
        dadosDisponibilidade.id,
        OpcaoDeAdicionalDeProduto.name,
        dtoDisponibilidade
      ).then(() => {
        resolve()
      })

    })
  }

  sincronizouDisponiblidadeCatalogo(produto: any, descricaoAlteracao: any = 'Sincronizou produto no catalogo'){
    return new Promise<void>((resolve, reject) => {
      let dtoDisponibilidade: any = {}

      dtoDisponibilidade.cardapioDelivery = produto.disponivelParaDelivery
      dtoDisponibilidade.cardapioMesa = produto.disponivelNaMesa

      this.insiraRegistroDeOperacao( String(`${descricaoAlteracao} "${produto.nome}"`) ,
        EnumOperacao.MudouDisponibilidadeDoProduto,
        produto.id,
        Produto.name,
        dtoDisponibilidade
      ).then(() => {
        resolve()
      })

    })

  }

  alterouPrecoProduto(dados: any,  descricaoAlteracao: any = 'Alterou o preço do produto '){
    return new Promise<void>((resolve, reject) => {
      this.insiraRegistroDeOperacao( String(`${descricaoAlteracao} "${dados.nome}"`) ,
        EnumOperacao.AlterouPreco,
        dados.id,
        Produto.name,
        dados.preco,
      ).then(() => {
        resolve()
      })
    })
  }

  alterouDisponibilidadeDoProdutoDaEmpresa(produto: any, descricaoAlteracao: any = 'Alterou a disponibilidade do produto '){
    return new Promise<void>((resolve, reject) => {
      this.insiraRegistroDeOperacao( String(`${descricaoAlteracao} "${produto.nome}"`) ,
        EnumOperacao.MudouDisponibilidadeDoProduto,
        produto.id,
        Produto.name,
        this.obtenhaDtoDisponibilidade(EnumDisponibilidadeProduto[produto.produtoNaEmpresa.disponibilidade], produto)
      ).then(() => {
        resolve()
      })
    })
  }

  private obtenhaDtoDisponibilidade(novaDisponibilidade: any, produto: any){
    let dtoDisponibilidade: any = {
      disponibilidade: novaDisponibilidade
    }

    if(produto.disponibilidade === EnumDisponibilidadeProduto.DiaDaSemana)
      dtoDisponibilidade.dias = produto.disponibilidades.map((disp: any) => disp.descricao)

    dtoDisponibilidade.cardapioDelivery = produto.disponivelParaDelivery
    dtoDisponibilidade.cardapioMesa = produto.disponivelNaMesa

    if(produto.insumoRemover)
      dtoDisponibilidade.vinculoRemovido =  `${produto.insumoRemover.id} - ${produto.insumoRemover.nome}`

    if(produto.insumo && produto.insumo.vincular)
      dtoDisponibilidade.vinculoAdicionado = `${produto.insumo.id} - ${produto.insumo.nome}`

    if(produto.estoque && produto.controlarEstoque)
      dtoDisponibilidade.estoque = produto.estoque

    return dtoDisponibilidade;

  }

  alterouDisponibilidadeDoProduto(produto: any, descricaoAlteracao: any = 'Alterou a disponibilidade do produto ') {
      return new Promise<void>((resolve, reject) => {
        this.insiraRegistroDeOperacao( String(`${descricaoAlteracao} "${produto.nome}"`) ,
          EnumOperacao.MudouDisponibilidadeDoProduto,
          produto.id,
          Produto.name,
          this.obtenhaDtoDisponibilidade(EnumDisponibilidadeProduto[produto.disponibilidade], produto)
          ).then(() => {
            resolve()
        })
      })
  }

  private obtenhaDTORaioDeCobranca(raio: any) {
      return {
        id: raio.id,
        tipoDeTaxa: raio.tipo,
        alcance: raio.alcance,
        valorFixo: raio.valorFixo,
        valorKmTaxa: raio.valorKmTaxa,
        valorMinimoTaxa: raio.valorMinimoTaxa,
        valorMinimoPedido: raio.valorMinimoPedido
      }
  }

  private obtenhaDTOZonaDeEntrega(zona: any) {
      return {
        id: zona.id,
        nome: zona.nome,
        valor: zona.valor,
        permiteFreteGratis: zona.permiteFreteGratis
      }
  }

  atualizouZonaEntrega(formaDeEntregaEmpresa: FormaDeEntregaEmpresa, zona: any, descricao: any = null){
    return new Promise<void>((resolve) => {
      let descricaoOperacao =  descricao || 'Atualizou zona de entrega: ' + zona.nome;
      this.insiraRegistroDeOperacao(
        descricaoOperacao,
        EnumOperacao.AtualizouFormaDeEntrega,
        formaDeEntregaEmpresa.id,
        FormaDeEntregaEmpresa.name,
        zona
      ).then(() => {resolve()})
    })
  }

  associouTabletAMesa(tablet: Tablet, mesaAnterior: Mesa){
    return new Promise<void>((resolve) => {
      let descricaoOperacao =  mesaAnterior ?    `Mudou tablet ${tablet.numero} de Mesa: ${mesaAnterior.nome} para ${tablet.mesa.nome}`  :
          `Associou tablet ${tablet.numero} a mesa ${tablet.mesa.nome}`;

      this.insiraRegistroDeOperacao(
        descricaoOperacao,
        EnumOperacao.AssociouTabletMesa,
        tablet.id,
        Tablet.name,
        tablet
      ).then(() => {resolve()})
    })
  }

  inseriuZonaEntrega(formaDeEntregaEmpresa: FormaDeEntregaEmpresa, zona: any){

    return new Promise<void>((resolve) => {
      this.insiraRegistroDeOperacao(
        'Inseriu zona de entrega: ' + zona.nome,
        EnumOperacao.AtualizouFormaDeEntrega,
        formaDeEntregaEmpresa.id,
        FormaDeEntregaEmpresa.name,
        zona
      ).then(() => {resolve()})
    })
    //,
  }

  atualizouFormaDeEntregaTipoDeCobranca(formaDeEntregaEmpresa: FormaDeEntregaEmpresa, descricao: string,
                                         operacao: EnumOperacao){

    let dtoFormaEntrega: any = {
      tipoDeCobranca: formaDeEntregaEmpresa.tipoDeCobranca
    }

    return new Promise<void>((resolve) => {
      this.insiraRegistroDeOperacao(
        descricao,
        operacao,
        formaDeEntregaEmpresa.id,
        FormaDeEntregaEmpresa.name,
        dtoFormaEntrega
      ).then(() => {resolve()})
    })

  }

  async atualizouFormaDePagamento(formaAtualizar: FormaDePagamento, descricao: string, operacao: EnumOperacao) {
    delete formaAtualizar.empresa;
    return new Promise<void>((resolve) => {
        this.insiraRegistroDeOperacao(
            descricao,
            operacao,
            formaAtualizar.id,
            FormaDePagamento.name,
            formaAtualizar
        ).then(() => {resolve()})
    })
  }

  async inseriuFormaDePagamento(forma: FormaDePagamento, descricao: string, operacao: EnumOperacao) {
    delete forma.empresa;
    return new Promise<void>((resolve) => {
        this.insiraRegistroDeOperacao(
            descricao,
            operacao,
            forma.id,
            FormaDePagamento.name,
           forma
        ).then(() => {resolve()})
    })
  }



  private atualizeRegistroFormaDeEntrega(formaDeEntregaEmpresa: FormaDeEntregaEmpresa, descricao: string,
                                         operacao: EnumOperacao, itensEditados: any) {
    return new Promise<void>((resolve) => {
      let dtoFormaEntrega: any = {
        id: formaDeEntregaEmpresa.id,
        ativa: formaDeEntregaEmpresa.ativa,
        permiteAgendamento: formaDeEntregaEmpresa.permiteAgendamento,
        agendamentoObrigatorio: formaDeEntregaEmpresa.agendamentoObrigatorio,
        tempoMinimo: formaDeEntregaEmpresa.tempoMinimo,
        tempoMaximo: formaDeEntregaEmpresa.tempoMaximo,
        valorMinimoFreteGratis: formaDeEntregaEmpresa.valorMinimoFreteGratis,
        valorMinimoPedido: formaDeEntregaEmpresa.valorMinimoPedido,
        valorMaximoPedido: formaDeEntregaEmpresa.valorMaximoPedido,
        selecionarBairroDaZona: formaDeEntregaEmpresa.selecionarBairroDaZona,
        tipoDeCobranca: formaDeEntregaEmpresa.tipoDeCobranca,
        bairroOpcional: formaDeEntregaEmpresa.bairroOpcional,
        cepObrigatorio: formaDeEntregaEmpresa.cepObrigatorio,
        naoUsarCidadePadrao: formaDeEntregaEmpresa.naoUsarCidadePadrao
      }

      if(formaDeEntregaEmpresa.tipoDeCobranca === EnumTipoDeCobranca.POR_DISTANCIA) {
        dtoFormaEntrega.raiosInseridos = itensEditados.raiosEditados.inseridos.map(
          (raio: any) => this.obtenhaDTORaioDeCobranca(raio)
        )
        dtoFormaEntrega.raiosRemovidos = itensEditados.raiosEditados.removidos.map(
          (raio: any) => this.obtenhaDTORaioDeCobranca(raio)
        )
        dtoFormaEntrega.raiosAtualizados = itensEditados.raiosEditados.atualizados.map(
          (raio: any) => this.obtenhaDTORaioDeCobranca(raio)
        )
      }

      if(formaDeEntregaEmpresa.tipoDeCobranca === EnumTipoDeCobranca.POR_ZONA) {
        dtoFormaEntrega.zonasInseridas = itensEditados.zonasEditadas.inseridos.map(
          (zona: any) => this.obtenhaDTOZonaDeEntrega(zona)
        )

        dtoFormaEntrega.zonasRemovidas = itensEditados.zonasEditadas.removidos.map(
          (zona: any) => this.obtenhaDTOZonaDeEntrega(zona)
        )

        dtoFormaEntrega.zonasAtualizadas = itensEditados.zonasEditadas.atualizados.map(
          (zona: any) => this.obtenhaDTOZonaDeEntrega(zona)
        )
      }

      this.insiraRegistroDeOperacao(
        descricao,
        operacao,
        formaDeEntregaEmpresa.id,
        FormaDeEntregaEmpresa.name,
        dtoFormaEntrega
      ).then(() => {resolve()})
    })
  }

  async atualizouRegistroFormaDeEntrega(formaDeEntregaEmpresa: FormaDeEntregaEmpresa, itensEditados: any) {
    return this.atualizeRegistroFormaDeEntrega(formaDeEntregaEmpresa, "Atualizou a forma de entrega "
      + formaDeEntregaEmpresa.formaDeEntrega.nome,
      EnumOperacao.AtualizouFormaDeEntrega, itensEditados)
  }

  atualizouHorarioDeFuncionamento(horarioDeFuncionamento: any) {
      return new Promise<void>((resolve) => {
        this.insiraRegistroDeOperacao(
          'Atualizou horário de funcionamento ' + DiaDaSemanaEnum[horarioDeFuncionamento.diaDaSemana] +
          ' - das ' + horarioDeFuncionamento.horarioAbertura + ' às ' + horarioDeFuncionamento.horarioFechamento,
          EnumOperacao.AtualizouHorarioFuncionamento,
          horarioDeFuncionamento.id,
          HorarioFuncionamento.name
        ).then(() => resolve())
    })

  }

  inseriuHorarioDeFuncionamento(horarioDeFuncionamento: any) {
    return new Promise<void>((resolve) => {
      this.insiraRegistroDeOperacao(
        'Inseriu horário de funcionamento ' + DiaDaSemanaEnum[horarioDeFuncionamento.diaDaSemana] +
        ' - das ' + horarioDeFuncionamento.horarioAbertura + ' às ' + horarioDeFuncionamento.horarioFechamento,
        EnumOperacao.InseriuHorarioDeFuncionamento,
        horarioDeFuncionamento.id,
        HorarioFuncionamento.name
      ).then(() => resolve())
    })
  }

  atualizouCupom(cupom: Cupom) {
      return new Promise<any>(async (resolve) => {
       await  this.insiraRegistroDeOperacao(
          'Atualizou o cupom ' + cupom.codigo,
          EnumOperacao.AtualizouCupom,
          cupom.id,
          Cupom.name,
          {
            id: cupom.id,
            nome: cupom.nome,
            codigo: cupom.codigo,
            tipo: cupom.tipo,
            restrito: cupom.restrito,
            restritoContatoPerdido: cupom.restritoContatoPerdido,
            restritoAniversariantes: cupom.restritoAniversariantes,
            primeiraCompra: cupom.primeiraCompra,
            ativo: cupom.ativo,
            validade: cupom.validade,
            valorMinimo: cupom.valorMinimo,
            qtdeMaxima: cupom.qtdeMaxima,
            quantidade: cupom.quantidade
          }
        )
        resolve(cupom);
      })

  }

  inseriuCupom(cupom: Cupom) {
    return new Promise<any>(async (resolve) => {
      await this.insiraRegistroDeOperacao(
        'Inseriu o cupom ' + cupom.codigo,
        EnumOperacao.InseriuCupom,
        cupom.id,
        Cupom.name,
        {
          id: cupom.id,
          nome: cupom.nome,
          codigo: cupom.codigo,
          tipo: cupom.tipo,
          restrito: cupom.restrito,
          restritoContatoPerdido: cupom.restritoContatoPerdido,
          restritoAniversariantes: cupom.restritoAniversariantes,
          primeiraCompra: cupom.primeiraCompra,
          ativo: cupom.ativo,
          validade: cupom.validade,
          valorMinimo: cupom.valorMinimo,
          qtdeMaxima: cupom.qtdeMaxima,
          quantidade: cupom.quantidade
        }
      )
      resolve(cupom)
    })
  }

  alterouPlano(contrato: Contrato){
    this.insiraRegistroDeOperacao( String(`Alterou o plano para "${contrato.plano.nome}"`) ,
      EnumOperacao.AlterouPlano, contrato.id, Contrato.name, { plano: contrato.plano.nome },  contrato.empresa);
  }

  alterouBloqueioAuto(empresa: Empresa){
    this.insiraRegistroDeOperacao( String(`Alterou data bloqueio`) ,
      EnumOperacao.AlterouBloqueioEmpresa, empresa.id, Empresa.name, { novoBloqueio: empresa.dataBloqueioAuto }, empresa);

  }

  alterouValorAtividade(empresa: any, atividade: any){
    let valorNovo: any = {};

    if(atividade.pontosGanhos) valorNovo.pontosGanhos = atividade.pontosGanhos;
    if(atividade.cashback) valorNovo.cashback = atividade.cashback;

    this.insiraRegistroDeOperacao( String(`Alterou valor atividade`) ,
      EnumOperacao.AlterouAtividade, atividade.id, Empresa.name, valorNovo, empresa);

  }

  alterouValorPontuarSoLoja(empresa: any, integracaoPedidoFidelidade: any){
    let valorNovo: any = { pontuarSoLoja: integracaoPedidoFidelidade.pontuarSoLoja};

    this.insiraRegistroDeOperacao( String(`Alterou configuração integraçao pedido fidelidade`) ,
      EnumOperacao.AlterouIntegracaoLoja, integracaoPedidoFidelidade.id, Empresa.name, valorNovo, empresa);

  }
  alterouValorPontuarMesas(empresa: any, integracaoPedidoFidelidade: any){
    let valorNovo: any = { pontuarMesas: integracaoPedidoFidelidade.pontuarMesas};

    this.insiraRegistroDeOperacao( String(`Alterou configuração integraçao pedido fidelidade`) ,
      EnumOperacao.AlterouIntegracaoLoja, integracaoPedidoFidelidade.id, Empresa.name, valorNovo, empresa);

  }

  alterouValorResgatarBrinde(empresa: any, integracaoPedidoFidelidade: any){
    let valorNovo: any = { resgatarBrinde: integracaoPedidoFidelidade.resgatarBrinde};

    this.insiraRegistroDeOperacao( String(`Alterou configuração integraçao pedido fidelidade`) ,
      EnumOperacao.AlterouIntegracaoLoja, integracaoPedidoFidelidade.id, Empresa.name, valorNovo, empresa);

  }

  alterouValorOcultarPontos(empresa: any, integracaoPedidoFidelidade: any){
    let valorNovo: any = { ocultarPontos: integracaoPedidoFidelidade.ocultarPontos};

    this.insiraRegistroDeOperacao( String(`Alterou configuração integraçao pedido fidelidade`) ,
      EnumOperacao.AlterouIntegracaoLoja, integracaoPedidoFidelidade.id, Empresa.name, valorNovo, empresa);

  }

  alterouValorContrato(contrato: Contrato){
    this.insiraRegistroDeOperacao( String(`Alterou valor negociado do contrato`) ,
      EnumOperacao.AlterouValorNegociado, contrato.id, Contrato.name, { valorNegociado: contrato.valorNegociado }, contrato.empresa);
  }

  alterouFormaDePagamento(assinatura: Assinatura){
    this.insiraRegistroDeOperacao( String(`Alterou forma de pagamento da assinatura`) ,
      EnumOperacao.AlterouFormaPagamentoAssinatura, assinatura.id, Assinatura.name,
      { formasDePagamento: assinatura.formasDePagamento }, assinatura.empresa);
  }

  removeuCartaoAssinatura(assinatura: Assinatura, empresa: any){
    this.insiraRegistroDeOperacao( String(`Removeu cartão da Assinatura`) ,
      EnumOperacao.RemoveuCartao, assinatura.id, Assinatura.name,      {   }, empresa);
  }

  supendeuAssinatura(assinatura: Assinatura, empresa: any){
    this.insiraRegistroDeOperacao( String(`Suspendeu Assinatura`) ,
      EnumOperacao.SuspendeuAssinatura, assinatura.id, Assinatura.name,      {   }, empresa);
  }

  adicionouItemAssinatura(assinatura: Assinatura, empresa: any, item: any){
    this.insiraRegistroDeOperacao( String(`Alterou Assinatura`) ,
      EnumOperacao.AlterouAssinatura, assinatura.id, Assinatura.name,      { novoItem: item   }, empresa);
  }

  inseriuAtividade(atividade: Atividade, empresa: any){
    let novoItem: any = Object.assign({} , atividade)

    novoItem.empresa = {id : atividade.empresa.id, nome: atividade.empresa.nome}
    novoItem.plano = { id: atividade.plano.id, nome: atividade.plano.nome}

    this.insiraRegistroDeOperacao( String(`Inseriu atividade`) ,
      EnumOperacao.AlterouPlanoFidelidade, atividade.id, Atividade.name,      { novoItem: novoItem   }, empresa);
  }

  atualizouAtividade(atividade: Atividade, empresa: any){
    let novoItem: any = Object.assign({} , atividade)

    novoItem.empresa = {id : atividade.empresa.id, nome: atividade.empresa.nome}
    novoItem.plano = { id: atividade.plano.id, nome: atividade.plano.nome}

    this.insiraRegistroDeOperacao( String(`Atualizou atividade`) ,
      EnumOperacao.AlterouPlanoFidelidade, atividade.id, Atividade.name,      { novoItem: novoItem   }, empresa);
  }

  removeuAtividade(atividade: Atividade, empresa: any){
    let novoItem: any = Object.assign({} , atividade)
    novoItem.empresa = {id : atividade.empresa.id, nome: atividade.empresa.nome}

    this.insiraRegistroDeOperacao( String(`Removeu atividade`) ,
      EnumOperacao.AlterouPlanoFidelidade, atividade.id, Atividade.name,      { novoItem: novoItem   }, empresa);
  }


  inseriuBrinde(brinde: Brinde, empresa: any){
    let novoItem: any = Object.assign({} , brinde)

    novoItem.empresa = {id : brinde.empresa.id, nome: brinde.empresa.nome}
    novoItem.plano = { id: brinde.plano.id, nome: brinde.plano.nome}

    this.insiraRegistroDeOperacao( String(`Inseriu brinde`) ,
      EnumOperacao.AlterouPlanoFidelidade, brinde.id, Brinde.name,      { novoItem: novoItem   }, empresa);
  }

  atualizouBrinde(brinde: Brinde, empresa: any){
    let novoItem: any = Object.assign({} , brinde)
    novoItem.empresa = {id : brinde.empresa.id, nome: brinde.empresa.nome}
    novoItem.plano = { id: brinde.plano.id, nome: brinde.plano.nome}

    this.insiraRegistroDeOperacao( String(`Atualizou brinde`) ,
      EnumOperacao.AlterouPlanoFidelidade, brinde.id, Brinde.name,      { novoItem: novoItem   }, empresa);
  }

  removeuBrinde(brinde: Brinde, empresa: any){
    let novoItem: any = Object.assign({} , brinde)
    novoItem.empresa = {id : brinde.empresa.id, nome: brinde.empresa.nome}

    this.insiraRegistroDeOperacao( String(`Removeu brinde`) ,
      EnumOperacao.AlterouPlanoFidelidade, brinde.id, Brinde.name,      { novoItem: novoItem   }, empresa);
  }



  atualizouPlano(plano: any, empresa: any){
    let novoItem: any = Object.assign({} , plano)

    delete novoItem.empresa;
    if(novoItem.tipoDePontuacao)
      delete novoItem.tipoDePontuacao.empresa;

    this.insiraRegistroDeOperacao( String(`Atualizou Plano Fidelidade`) ,
      EnumOperacao.AlterouPlanoFidelidade, plano.id, Plano.name,      { novoItem: novoItem   }, empresa);
  }



  adicionouPlano(plano: Plano, empresa: any){
    let novoItem: any = Object.assign({} , plano)

    delete novoItem.empresa;
    if(novoItem.tipoDePontuacao)
      delete novoItem.tipoDePontuacao.empresa;

    this.insiraRegistroDeOperacao( String(`Novo Plano Fidelidade`) ,
      EnumOperacao.InseriuPlanoFidelidade, plano.id, Plano.name,      { novoItem: novoItem   }, empresa);
  }

  removeuPlano(plano: Plano, empresa: any){
    this.insiraRegistroDeOperacao( String(`Removeu plano Fidelidade`) ,
      EnumOperacao.AlterouPlanoFidelidade, plano.id, Plano.name,      { id: plano.id   }, empresa);
  }

  ativouPlano(plano: any, empresa: any){
    this.insiraRegistroDeOperacao( String(`Ativou plano Fidelidade`) ,
      EnumOperacao.AlterouPlanoFidelidade, plano.id, Plano.name,      { id: plano.id   }, empresa);
  }

  desativouPlano(plano: any, empresa: any){
    this.insiraRegistroDeOperacao( String(`Desativou plano Fidelidade`) ,
      EnumOperacao.AlterouPlanoFidelidade, plano.id, Plano.name,      { id: plano.id   }, empresa);
  }

  removeuItemAssinatura(assinatura: Assinatura, empresa: any, item: any){
    this.insiraRegistroDeOperacao( String(`Alterou Assinatura`) ,
      EnumOperacao.AlterouAssinatura, assinatura.id, Assinatura.name,      { itemRemovido: item   }, empresa);
  }

  ativouAssinatura(assinatura: Assinatura, empresa: any){
    this.insiraRegistroDeOperacao( String(`Ativou Assinatura`) ,
      EnumOperacao.AtivouAssinatura, assinatura.id, Assinatura.name,      {   }, empresa);
  }

  async inseriuPromocao(promocao: Promocao) {
    this.insiraRegistroDeOperacao(String('Inseriu Promoção'),
      EnumOperacao.InseriuPromocao, promocao.id, Promocao.name, {
    id: promocao.id,
    descricao: promocao.descricao,
    dataInicio: promocao.dataInicio,
    dataFim: promocao.dataFim,
    ativa: promocao.ativa,
    regras: promocao.regras

  })
  }

  async atualizouPromocao(promocao: any) {
    this.insiraRegistroDeOperacao(String('Atualizou Promoção'),
      EnumOperacao.AtualizouPromocao, promocao.id, Promocao.name, {
        id: promocao.id,
        descricao: promocao.descricao,
        dataInicio: promocao.dataInicio,
        dataFim: promocao.dataFim,
        ativa: promocao.ativa,
        regras: promocao.regras

      })

  }

  async removeuHorarioDeFuncionamento(horarioDeFuncionamento: any) {
    return new Promise<void>((resolve) => {
      this.insiraRegistroDeOperacao(
        'Removeu horário de funcionamento ' + DiaDaSemanaEnum[horarioDeFuncionamento.diaDaSemana] +
        ' - das ' + horarioDeFuncionamento.horarioAbertura + ' às ' + horarioDeFuncionamento.horarioFechamento,
        EnumOperacao.RemoveuHorarioFuncionamento,
        horarioDeFuncionamento.id,
        HorarioFuncionamento.name
      ).then(() => resolve())
    })

  }

  async ativouIntegracaoAPI(id: any) {
    return new Promise<void>((resolve) => {
      this.insiraRegistroDeOperacao(
        'Ativou chave de integração via API com id ' + id,
        id,
      )
    })
  }

  ativouOuDesativouChatbotMia(empresa: any, statusMia: EnumStatusMia) {
    return new Promise<void>(resolve => {
      let dtoChatbot: any = {
        id: empresa.id,
        nome: empresa.nome,
        statusMia: statusMia
      }

      this.insiraRegistroDeOperacao(
        'Usuário alterou o status do chatbot Mia para ' + statusMia +
        (empresa && empresa.nome ? ' na empresa ' + empresa.nome : ''),
        EnumOperacao.AlterouMia, // Remova a referência a EnumOperacao aqui
        empresa && empresa.id ? empresa.id : null,
        empresa && empresa.id ? Empresa.name : null,
        dtoChatbot
      ).then(() => {
        resolve()
      })
    })
  }


  limpouCatalogo(empresa: any){
    return new Promise<void>(resolve => {
      this.insiraRegistroDeOperacao(
        'Catalogo foi zerado: produtos e categorias removidos.',
        EnumOperacao.RemoveuCatalogo, // Remova a referência a EnumOperacao aqui
        empresa.id , Empresa.name, {}
      ).then(() => {
        resolve()
      })
    })
  }

  importouProdutosDoCatalogo(empresa: any, catalogoDestino: any, totalProdutos: number){
    return new Promise<void>(resolve => {
      this.insiraRegistroDeOperacao(
        `Catalogo importado de ${catalogoDestino.id} - ${catalogoDestino.nome}, total produtos: ${totalProdutos}`,
        EnumOperacao.ImportouCatalogo,
        empresa.id , Empresa.name, {}
      ).then(() => {
        resolve()
      })
    })
  }

  async atualizouEstoqueAtivo(produto: any, estoque: any  , descricao: string ) {
    return new Promise<void>(resolve => {
      this.insiraRegistroDeOperacao(
        descricao,
        EnumOperacao.MudouDisponibilidadeDoProduto,
        produto.id , Produto.name, {}
      ).then(() => {
        resolve()
      })
    })
  }
}
