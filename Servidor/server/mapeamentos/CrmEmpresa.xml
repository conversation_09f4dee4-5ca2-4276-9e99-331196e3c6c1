<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="CrmEmpresa">

  <!-- ResultMap básico -->
  <resultMap id="crmEmpresaRM" type="CrmEmpresa">
    <id property="id" column="crm_empresa_id"/>
    <result property="nome" column="crm_empresa_nome"/>
    <result property="cnpj" column="crm_empresa_cnpj"/>
    <result property="telefone" column="crm_empresa_telefone"/>
    <result property="email" column="crm_empresa_email"/>
    <result property="endereco" column="crm_empresa_endereco"/>
    <result property="ativa" column="crm_empresa_ativa"/>
    <result property="createdAt" column="crm_empresa_created_at"/>
    <result property="updatedAt" column="crm_empresa_updated_at"/>
  </resultMap>

  <!-- Seleção geral -->
  <select id="selecione" parameterType="map" resultMap="crmEmpresaRM" prefix="true">
    select * from crm_empresa
    where
    1 = 1
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="nome != null">
        and nome like concat('%', #{nome}, '%')
      </if>
      <if test="cnpj != null">
        and cnpj = #{cnpj}
      </if>
      <if test="ativa != null">
        and ativa = #{ativa}
      </if>
    order by nome
    <if test="inicio != null">
      limit #{inicio}, #{total}
    </if>
  </select>

  <select id="selecioneTotal" parameterType="map" resultType="int">
    select count(*) from crm_empresa
    where
    1 = 1
      <if test="nome != null">
        and nome like concat('%', #{nome}, '%')
      </if>
      <if test="ativa != null">
        and ativa = #{ativa}
      </if>
  </select>

  <!-- Inserção -->
  <insert id="insira" parameterType="CrmEmpresa" useGeneratedKeys="true" keyProperty="id">
    insert into crm_empresa (
      nome, cnpj, telefone, email, endereco, ativa, created_at, updated_at
    ) values (
      #{nome}, #{cnpj}, #{telefone}, #{email}, #{endereco}, #{ativa}, now(), now()
    )
  </insert>

  <!-- Atualização -->
  <update id="atualize" parameterType="CrmEmpresa">
    update crm_empresa set
      nome = #{nome},
      cnpj = #{cnpj},
      telefone = #{telefone},
      email = #{email},
      endereco = #{endereco},
      ativa = #{ativa},
      updated_at = now()
    where id = #{id}
  </update>

  <!-- Remoção -->
  <delete id="remova" parameterType="map">
    delete from crm_empresa where id = #{id}
  </delete>

</mapper>
