<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="CrmTelefoneLead">
  <!-- ResultMap básico -->
  <resultMap id="crmTelefoneLeadRM" type="CrmTelefoneLead">
    <id property="id" column="crm_lead_telefone_id"/>
    <result property="crmLeadId" column="crm_lead_telefone_crm_lead_id"/>
    <result property="tipo" column="crm_lead_telefone_tipo"/>
    <result property="numero" column="crm_lead_telefone_numero"/>
    <result property="descricao" column="crm_lead_telefone_descricao"/>
    <result property="ativo" column="crm_lead_telefone_ativo"/>
    <result property="ordem" column="crm_lead_telefone_ordem"/>
    <result property="createdAt" column="crm_lead_telefone_created_at"/>
    <result property="updatedAt" column="crm_lead_telefone_updated_at"/>
  </resultMap>

  <!-- Seleção por lead -->
  <select id="selecione" parameterType="map" resultMap="crmTelefoneLeadRM" prefix="true">
    select * from crm_lead_telefone
    <where>
      <if test="crmLeadId != null">
        crm_lead_id = #{crmLeadId}
      </if>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="tipo != null">
        and tipo = #{tipo}
      </if>
      <if test="ativo != null">
        and ativo = #{ativo}
      </if>
    </where>
    order by ordem asc, created_at asc
  </select>

  <!-- Inserção -->
  <insert id="insira" parameterType="CrmTelefoneLead" useGeneratedKeys="true" keyProperty="id">
    insert into crm_lead_telefone (
      crm_lead_id, tipo, numero, descricao, ativo, ordem, created_at, updated_at
    ) values (
      #{crmLeadId}, #{tipo}, #{numero}, #{descricao}, #{ativo}, #{ordem}, now(), now()
    )
  </insert>

  <!-- Atualização -->
  <update id="atualize" parameterType="CrmTelefoneLead">
    update crm_lead_telefone set
      tipo = #{tipo},
      numero = #{numero},
      descricao = #{descricao},
      ativo = #{ativo},
      ordem = #{ordem},
      updated_at = now()
    where id = #{id}
  </update>

  <!-- Remoção -->
  <delete id="remova" parameterType="map">
    delete from crm_lead_telefone where id = #{id}
  </delete>

  <!-- Remoção por lead -->
  <delete id="removaPorLead" parameterType="map">
    delete from crm_lead_telefone where crm_lead_id = #{crmLeadId}
  </delete>

  <!-- Remoção por tipo -->
  <delete id="removaPorTipo" parameterType="map">
    delete from crm_lead_telefone where crm_lead_id = #{crmLeadId} and tipo = #{tipo}
  </delete>

  <!-- Desativar telefone -->
  <update id="desativar" parameterType="map">
    update crm_lead_telefone set 
      ativo = 0, 
      updated_at = now() 
    where id = #{id}
  </update>

  <!-- Reativar telefone -->
  <update id="reativar" parameterType="map">
    update crm_lead_telefone set 
      ativo = 1, 
      updated_at = now() 
    where id = #{id}
  </update>

  <!-- Atualizar ordem -->
  <update id="atualizarOrdem" parameterType="map">
    update crm_lead_telefone set 
      ordem = #{ordem}, 
      updated_at = now() 
    where id = #{id}
  </update>

  <!-- Contar telefones por lead -->
  <select id="contar" parameterType="map" resultType="map">
    select count(*) as total 
    from crm_lead_telefone 
    <where>
      <if test="crmLeadId != null">
        crm_lead_id = #{crmLeadId}
      </if>
      <if test="ativo != null">
        and ativo = #{ativo}
      </if>
      <if test="tipo != null">
        and tipo = #{tipo}
      </if>
    </where>
  </select>

  <!-- Buscar telefone principal (WhatsApp ou primeiro ativo) -->
  <select id="buscarPrincipal" parameterType="map" resultMap="crmTelefoneLeadRM">
    select * from crm_lead_telefone 
    where crm_lead_id = #{crmLeadId} and ativo = 1
    order by 
      case when tipo = 'WhatsApp' then 1 else 2 end,
      ordem asc, 
      created_at asc
    limit 1
  </select>

</mapper>