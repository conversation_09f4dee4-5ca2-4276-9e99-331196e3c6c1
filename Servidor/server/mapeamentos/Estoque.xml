<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="estoque">
  <resultMap id="estoqueRM" type="Estoque">
    <id property="id" column="estoque_id"/>

    <result property="quantidade" column="estoque_quantidade"/>
    <result property="quantidadeMinima" column="estoque_quantidade_minima"/>
    <result property="quantidadeReservada" column="estoque_quantidade_reservada"/>
    <result property="ultimaAtualizacao" column="estoque_ultima_atualizacao"/>
    <result property="ativo" column="estoque_ativo"/>
  </resultMap>

  <select id="selecione"  parameterType="map" resultMap="estoqueRM" prefix="true">
    select * from estoque where id = #{id}
  </select>

  <select id="selecioneParaUpdate"  parameterType="map" resultMap="estoqueRM" prefix="true">
    select * from estoque where id = #{id} FOR UPDATE;
  </select>

  <update id="atualize">
    update estoque set   quantidade_minima   = #{quantidadeMinima}
        where id  = #{id}
  </update>


  <update id="atualizeQuatidadeEstoque">
    update estoque set quantidade = #{quantidade}  , ultima_atualizacao = now()
    where id  = #{id}
  </update>

  <update id="atualizesQuatidadeEstoque">
    update estoque set quantidade = #{quantidade},
                       quantidade_reservada = #{quantidadeReservada} , ultima_atualizacao = now()
        where id  = #{id}
  </update>

  <update id="atualizeQuantidadeReservada">
    update estoque set quantidade_reservada = #{quantidadeReservada}, ultima_atualizacao = now()    where id  = #{id}
  </update>

  <update id="atualizeEstoqueProduto" parameterType="map" >
    update produto
      set estoque_id = #{estoque.id}
        where id = #{id}
  </update>

  <update id="atualizeEstoqueAtivo" parameterType="map" >
    update estoque
      set ativo = #{ativo}
        where id = #{id}
  </update>


</mapper>
