<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="LeadLink">
  <!-- ResultMap básico -->
  <resultMap id="leadLinkRM" type="LeadLink">
    <id property="id" column="crm_lead_link_id"/>
    <result property="crmLeadId" column="crm_lead_link_crm_lead_id"/>
    <result property="tipo" column="crm_lead_link_tipo"/>
    <result property="url" column="crm_lead_link_url"/>
    <result property="descricao" column="crm_lead_link_descricao"/>
    <result property="ativo" column="crm_lead_link_ativo"/>
    <result property="ordem" column="crm_lead_link_ordem"/>
    <result property="createdAt" column="crm_lead_link_created_at"/>
    <result property="updatedAt" column="crm_lead_link_updated_at"/>
  </resultMap>

  <!-- Sele<PERSON> por lead -->
  <select id="selecione" parameterType="map" resultMap="leadLinkRM" prefix="true">
    select * from crm_lead_link
    <where>
      <if test="crmLeadId != null">
        crm_lead_id = #{crmLeadId}
      </if>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="tipo != null">
        and tipo = #{tipo}
      </if>
      <if test="ativo != null">
        and ativo = #{ativo}
      </if>
    </where>
    order by ordem asc, created_at asc
  </select>

  <!-- Inserção -->
  <insert id="insira" parameterType="LeadLink" useGeneratedKeys="true" keyProperty="id">
    insert into crm_lead_link (
      crm_lead_id, tipo, url, descricao, ativo, ordem, created_at, updated_at
    ) values (
      #{crmLeadId}, #{tipo}, #{url}, #{descricao}, #{ativo}, #{ordem}, now(), now()
    )
  </insert>

  <!-- Atualização -->
  <update id="atualize" parameterType="LeadLink">
    update crm_lead_link set
      tipo = #{tipo},
      url = #{url},
      descricao = #{descricao},
      ativo = #{ativo},
      ordem = #{ordem},
      updated_at = now()
    where id = #{id}
  </update>

  <!-- Remoção -->
  <delete id="remova" parameterType="map">
    delete from crm_lead_link where id = #{id}
  </delete>

  <!-- Remoção por lead -->
  <delete id="removaPorLead" parameterType="map">
    delete from crm_lead_link where crm_lead_id = #{crmLeadId}
  </delete>

  <!-- Remoção por tipo -->
  <delete id="removaPorTipo" parameterType="map">
    delete from crm_lead_link where crm_lead_id = #{crmLeadId} and tipo = #{tipo}
  </delete>

</mapper>
