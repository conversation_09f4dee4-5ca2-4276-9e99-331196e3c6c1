<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="planoempresarial">
  <resultMap id="planoEmpresarialRM" type="PlanoEmpresarial">
    <id property="id" column="plano_empresarial_id"/>

    <result property="nome" column="plano_empresarial_nome"/>
    <result property="valor" column="plano_empresarial_valor"/>
    <result property="identificador" column="plano_empresarial_identificador"/>
    <result property="codigo" column="plano_empresarial_codigo"/>
    <result property="descontoCartao" column="plano_empresarial_desconto_cartao"/>
    <result property="limiteContatos" column="plano_empresarial_limite_contatos"/>
    <result property="limiteSms" column="plano_empresarial_limite_sms"/>
    <result property="limitePedidos" column="plano_empresarial_limite_pedidos"/>
    <result property="publico" column="plano_empresarial_publico"/>
    <result property="recomendado" column="plano_empresarial_recomendado"/>
    <result property="intervalo" column="plano_empresarial_intervalo"/>

    <collection property="vantagens"   resultMap="planoVantagem.planoVantagemRM"/>
    <collection  property="modulos"  columnPrefix=""  resultMap="modulo.moduloPlanoRM"/>
    <collection property="planosIugu"   resultMap="planoIugu.planoIuguRM"/>

  </resultMap>



  <select id="selecione" parameterType="map" resultMap="planoEmpresarialRM" prefix="true">
    select plano_empresarial.*,
           plano_iugu.*,
           plano_vantagem.*, vantagem.*, modulo_plano.*
    from  plano_empresarial left join plano_iugu on plano_empresarial.id = plano_empresarial_id
                            left join plano_vantagem on plano_vantagem.plano_empresarial_id = plano_empresarial.id
                            left join vantagem on   vantagem.id = vantagem_id
                            left join plano_modulo on plano_modulo.plano_empresarial_id = plano_empresarial.id
                            left join modulo modulo_plano on plano_modulo.modulo_id = modulo_plano.id
        <choose>
          <when test="id">
            where plano_empresarial.id = #{id}
          </when>
          <when test="identificador">
            where plano_empresarial.identificador = #{identificador}
          </when>
          <when test="publico">
            where plano_empresarial.publico is true order by plano_empresarial.valor, plano_vantagem.ordem
          </when>
          <otherwise>
            order by valor
          </otherwise>
        </choose>
  </select>

  <insert id="insira">
    insert into
      plano_empresarial(nome, valor, desconto_cartao, limite_contatos, limite_sms, limite_pedidos, ativo, intervalo)
        values (#{nome}, #{valor}, #{descontoCartao}, #{limiteContatos}, #{limiteSms}, #{limitePedidos}, #{ativo}, #{intervalo})
  </insert>

  <update id="atualize" parameterType="map"   >
    update plano_empresarial
        set nome =  #{nome}, codigo = #{codigo}, intervalo = #{intervalo},
            valor = #{valor}, identificador = #{identificador}
            where id = #{id};
  </update>

 <update id="atualizePublico" parameterType="map"   >
    update plano_empresarial
        set publico =  #{publico}
            where id = #{id};
  </update>



</mapper>
