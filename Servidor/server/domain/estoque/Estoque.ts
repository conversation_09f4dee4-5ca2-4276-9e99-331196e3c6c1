import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorBasico} from "../../mapeadores/MapeadorBasico";
import {MapeadorDeEstoque} from "../../mapeadores/MapeadorDeEstoque";

export class Estoque extends ObjetoPersistente{
  public quantidade: number;
  public quantidadeMinima = 0;
  public quantidadeReservada = 0;
  public ativo = true;
  constructor() {
     super();
  }
  static crieDaCache(dados: any) {
    let estoque = Object.assign(new Estoque(), dados)

    return estoque;
  }


  temDisponivel(qtde: number) {
    //estoque foi desativado
    if(!this.ativo) return true;

    const qtdeNoEstoque: number =  this.getQuantidade() ;

   // console.log(`qtde no estoque ${this.id}: ${qtdeNoEstoque}`)

    return qtdeNoEstoque >= qtde;
  }

  getQuantidade(){
    return this.formateNumero(this.quantidade - this.quantidadeReservada)
  }

  getNivelEstoque() {
    return  this.formateNumero(this.getQuantidade() / this.quantidadeMinima)
  }

  formateNumero(numero: number){
    return  Number(numero.toFixed(3))
  }

  acimaMinimo() {
    return  this.getQuantidade() > this.quantidadeMinima
  }

  mapeador(): MapeadorBasico {
    return new MapeadorDeEstoque();
  }

}
