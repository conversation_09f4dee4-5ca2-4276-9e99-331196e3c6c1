export interface Socio {
  nome: string;
  cargo?: string;
  dataEntrada?: string;
  principal?: boolean;
  scoreAnalise?: number;
  motivoSelecao?: string;
}

export class CrmEmpresa {
  id?: number;
  nome = '';
  cnpj?: string;
  telefone?: string;
  email?: string;
  endereco?: string;
  ativa = true;
  socios?: Socio[];
  createdAt?: Date;
  updatedAt?: Date;

  constructor(nome?: string) {
    if (nome) {
      this.nome = nome.trim();
    }
  }


  // Validações
  isValid(): boolean {
    return this.nome && this.nome.trim().length > 0;
  }

  // Formatação de CNPJ
  formatarCnpj(): string {
    if (!this.cnpj) return '';
    return this.cnpj.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
  }

  // Formatação de telefone
  formatarTelefone(): string {
    if (!this.telefone) return '';
    const telefone = this.telefone.replace(/\D/g, '');
    if (telefone.length === 11) {
      return telefone.replace(/^(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
    } else if (telefone.length === 10) {
      return telefone.replace(/^(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
    }
    return this.telefone;
  }

  // Status da empresa
  getStatus(): string {
    return this.ativa ? 'Ativa' : 'Inativa';
  }

  // Resumo para exibição
  getResumo(): string {
    return `${this.nome}${this.cnpj ? ` (${this.formatarCnpj()})` : ''}`;
  }

  // ===== MÉTODOS PARA GERENCIAR SÓCIOS =====

  /**
   * Adiciona um sócio à empresa
   */
  adicionarSocio(socio: Socio): void {
    if (!this.socios) this.socios = [];
    this.socios.push(socio);
  }

  /**
   * Adiciona múltiplos sócios à empresa
   */
  adicionarSocios(socios: Socio[]): void {
    if (!socios || socios.length === 0) return;
    if (!this.socios) this.socios = [];
    this.socios.push(...socios);
  }

  /**
   * Obtém o sócio principal da empresa
   */
  getSocioPrincipal(): Socio | undefined {
    return this.socios?.find(socio => socio.principal);
  }

  /**
   * Obtém todos os sócios da empresa
   */
  getSocios(): Socio[] {
    return this.socios || [];
  }

  /**
   * Obtém a quantidade de sócios
   */
  getQuantidadeSocios(): number {
    return this.socios?.length || 0;
  }

  /**
   * Verifica se a empresa tem sócios cadastrados
   */
  hasSocios(): boolean {
    return this.socios && this.socios.length > 0;
  }

  /**
   * Remove um sócio pelo nome
   */
  removerSocio(nome: string): boolean {
    if (!this.socios) return false;
    const index = this.socios.findIndex(socio => socio.nome === nome);
    if (index >= 0) {
      this.socios.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * Define um sócio como principal (e remove o flag dos outros)
   */
  definirSocioPrincipal(nome: string): boolean {
    if (!this.socios) return false;
    
    let found = false;
    this.socios.forEach(socio => {
      if (socio.nome === nome) {
        socio.principal = true;
        found = true;
      } else {
        socio.principal = false;
      }
    });
    
    return found;
  }
}
