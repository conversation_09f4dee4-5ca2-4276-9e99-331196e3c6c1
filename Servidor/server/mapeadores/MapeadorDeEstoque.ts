import {MapeadorBasicoComCache} from "./MapeadorBasicoComCache";
import {Estoque} from "../domain/estoque/Estoque";

export class MapeadorDeEstoque extends   MapeadorBasicoComCache {
  constructor() {
    super('estoque', false);
  }

  selecioneParaUpdate(id: number){
    return this.gerenciadorDeMapeamentos.selecioneUmAsync(this.metodo('selecioneParaUpdate'), {id: id} );
  }

  atualizeQuatidadesEstoque(estoque: any){
    return this.gerenciadorDeMapeamentos.insiraAsync(this.metodo('atualizesQuatidadeEstoque'), estoque);
  }

  atualizeQuatidadeEstoque(estoque: any){
    return this.gerenciadorDeMapeamentos.insiraAsync(this.metodo('atualizeQuatidadeEstoque'), estoque);
  }

  atualizeQuantidadeReservada(estoque: any){
    return this.gerenciadorDeMapeamentos.insiraAsync(this.metodo('atualizeQuantidadeReservada'), estoque);
  }

  atualizeEstoqueProduto(produto: any){
    return this.gerenciadorDeMapeamentos.insiraAsync(this.metodo('atualizeEstoqueProduto'), produto);
  }

  atualizeEstoqueAtivo(estoque: any){
    return this.gerenciadorDeMapeamentos.insiraAsync(this.metodo('atualizeEstoqueAtivo'), estoque);
  }

  crieObjetoDaCache(json: any): any {
    return  Estoque.crieDaCache(json)
  }

  async selecioneEstoque(id: number){
    let estoque = await this.selecioneCachePoId(id);

    return estoque
  }


}
