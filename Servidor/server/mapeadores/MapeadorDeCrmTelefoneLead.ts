import { MapeadorBasico } from "./MapeadorBasico";

export default class MapeadorDeCrmTelefoneLead extends MapeadorBasico {
  constructor() {
    super('CrmTelefoneLead');
    // CRM independente - desativar contexto multi-tenant
    this.desativeMultiCliente();
  }

  // Buscar telefones por lead
  async buscarPorLead(crmLeadId: number): Promise<any[]> {
    return this.listeAsync({ crmLeadId });
  }

  // Buscar telefone específico por lead e tipo
  async buscarPorLeadETipo(crmLeadId: number, tipo: string): Promise<any> {
    const telefones = await this.listeAsync({ crmLeadId, tipo });
    return telefones.length > 0 ? telefones[0] : null;
  }

  // Buscar telefones ativos por lead
  async buscarAtivosPorLead(crmLeadId: number): Promise<any[]> {
    return this.listeAsync({ crmLeadId, ativo: true });
  }

  // Buscar telefone principal (primeiro WhatsApp ou primeiro ativo)
  async buscarPrincipalPorLead(crmLeadId: number): Promise<any> {
    // Primeiro tenta buscar WhatsApp
    const whatsapp = await this.buscarPorLeadETipo(crmLeadId, 'WhatsApp');
    if (whatsapp) return whatsapp;
    
    // Se não tiver WhatsApp, busca o primeiro ativo
    const telefones = await this.buscarAtivosPorLead(crmLeadId);
    return telefones.length > 0 ? telefones[0] : null;
  }

  // Remover todos os telefones de um lead
  async removerPorLead(crmLeadId: number): Promise<void> {
    return this.gerenciadorDeMapeamentos.atualizeAsync(
      this.metodo('removaPorLead'), 
      { crmLeadId }
    );
  }

  // Remover telefone por tipo
  async removerPorTipo(crmLeadId: number, tipo: string): Promise<void> {
    return this.gerenciadorDeMapeamentos.atualizeAsync(
      this.metodo('removaPorTipo'), 
      { crmLeadId, tipo }
    );
  }

  // Salvar múltiplos telefones de uma vez
  async salvarTelefones(crmLeadId: number, telefones: any[]): Promise<void> {
    console.log('MAPEADOR_TELEFONE: Salvando telefones para lead ID:', crmLeadId);
    console.log('MAPEADOR_TELEFONE: Tipo do crmLeadId:', typeof crmLeadId);
    console.log('MAPEADOR_TELEFONE: Telefones a serem salvos:', telefones);

    if (!crmLeadId || crmLeadId === null || crmLeadId === undefined) {
      throw new Error(`crmLeadId inválido: ${crmLeadId}`);
    }

    // Primeiro remove todos os telefones existentes
    await this.removerPorLead(crmLeadId);

    // Depois insere os novos telefones
    for (const telefone of telefones) {
      telefone.crmLeadId = crmLeadId;
      console.log('MAPEADOR_TELEFONE: Inserindo telefone:', telefone);
      await this.insiraSync(telefone);
    }

    console.log('MAPEADOR_TELEFONE: Todos os telefones foram salvos com sucesso');
  }

  // Atualizar telefone específico
  async atualizarTelefone(telefone: any): Promise<void> {
    console.log('MAPEADOR_TELEFONE: Atualizando telefone ID:', telefone.id);
    return this.atualizeSync(telefone);
  }

  // Desativar telefone (soft delete)
  async desativarTelefone(id: number): Promise<void> {
    return this.gerenciadorDeMapeamentos.atualizeAsync(
      this.metodo('desativar'), 
      { id }
    );
  }

  // Reativar telefone
  async reativarTelefone(id: number): Promise<void> {
    return this.gerenciadorDeMapeamentos.atualizeAsync(
      this.metodo('reativar'), 
      { id }
    );
  }

  // Reordenar telefones
  async reordenarTelefones(crmLeadId: number, telefoneIds: number[]): Promise<void> {
    for (let i = 0; i < telefoneIds.length; i++) {
      await this.gerenciadorDeMapeamentos.atualizeAsync(
        this.metodo('atualizarOrdem'), 
        { id: telefoneIds[i], ordem: i }
      );
    }
  }

  // Contar telefones ativos por lead
  async contarPorLead(crmLeadId: number): Promise<number> {
    const resultado = await this.gerenciadorDeMapeamentos.obtenhaPrimeiroAsync(
      this.metodo('contar'), 
      { crmLeadId, ativo: true }
    );
    return resultado ? resultado.total : 0;
  }
}