import {Router} from 'express';
import MapeadorDeCrmEmpresa from '../mapeadores/MapeadorDeCrmEmpresa';
import {Resposta} from '../utils/Resposta';

const router: Router = Router();

// Listagem com filtros
router.get('/', async (req: any, res) => {
  const mapeador = new MapeadorDeCrmEmpresa();
  try {
    const params = {
      inicio: req.query.inicio ? parseInt(req.query.inicio) : 0,
      total: req.query.total ? parseInt(req.query.total) : 50,
      nome: req.query.nome || null,
      ativa: req.query.ativa !== undefined ? req.query.ativa === 'true' : null
    };

    const dados = await mapeador.listeAsync(params);
    const total = await mapeador.selecioneTotal(params);
    res.json(Resposta.sucesso({ data: dados, total }));
  } catch (err) {
    console.error('Erro ao listar empresas CRM', err);
    res.json(Resposta.erro('Erro ao listar empresas CRM'));
  }
});

// Seleção por ID
router.get('/:id', async (req: any, res) => {
  const mapeador = new MapeadorDeCrmEmpresa();
  try {
    const empresa = await mapeador.selecioneSync({ id: parseInt(req.params.id) });
    res.json(Resposta.sucesso(empresa));
  } catch (err) {
    console.error('Erro ao buscar empresa CRM', err);
    res.json(Resposta.erro('Erro ao buscar empresa CRM'));
  }
});

// Inserção/Atualização
router.post('/', async (req: any, res) => {
  const mapeador = new MapeadorDeCrmEmpresa();
  try {
    const empresa = req.body;
    
    if (empresa.id) {
      await mapeador.atualizeSync(empresa);
    } else {
      await mapeador.insiraSync(empresa);
    }
    
    res.json(Resposta.sucesso(empresa));
  } catch (err) {
    console.error('Erro ao salvar empresa CRM', err);
    res.json(Resposta.erro('Erro ao salvar empresa CRM'));
  }
});

// Remoção
router.delete('/:id', async (req: any, res) => {
  const mapeador = new MapeadorDeCrmEmpresa();
  try {
    await mapeador.removaAsync({ id: parseInt(req.params.id) });
    res.json(Resposta.sucesso({ removido: true }));
  } catch (err) {
    console.error('Erro ao remover empresa CRM', err);
    res.json(Resposta.erro('Erro ao remover empresa CRM'));
  }
});

export const CrmEmpresasController: Router = router; 