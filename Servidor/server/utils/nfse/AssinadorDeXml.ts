let SignedXml = require('xml-crypto').SignedXml
  , fs = require('fs')


export class AssinadorDeXml {
  private caminhoCertificado
    : string;
  static assine(xml: string): string {

    return new AssinadorDeXml("client.pem").assine(xml);
  }

  constructor(caminhoCertificado: string) {
    this.caminhoCertificado = caminhoCertificado
  }

  static assineNfse(xml: string): string {


    xml = xml.replace(
      /(\r\n\t|\n|\r\t|\r\n|\r)/gm,"").replace(/>\s+</g,"><").trim()
    xml = '<?xml version="1.0"?>\n' + xml

    let sig = new SignedXml()
    sig.addReference("//*[local-name(.)='GerarNfseEnvio']", ['http://www.w3.org/2000/09/xmldsig#enveloped-signature', 'http://www.w3.org/TR/2001/REC-xml-c14n-20010315'],
      "http://www.w3.org/2000/09/xmldsig#sha1", "", "", "", true)
    sig.signingKey = fs.readFileSync("client.pem")
    sig.keyInfoProvider = new MyKeyInfo()
    sig.canonicalizationAlgorithm = "http://www.w3.org/TR/2001/REC-xml-c14n-20010315"
    sig.computeSignature(xml,  { location: {reference: "//*[local-name(.)='InfDeclaracaoPrestacaoServico']", action: 'after'}})
    return sig.getSignedXml()
  }


  assine(xml: string,
         localAssinatura: any = { location: {reference: "//*[local-name(.)='InfDeclaracaoPrestacaoServico']", action: 'after'}},
         referencia: string = "//*[local-name(.)='GerarNfseEnvio']", inserirVersaoXml: boolean = true): string {


    xml = xml.replace(
      /(\r\n\t|\n|\r\t|\r\n|\r)/gm,"").replace(/>\s+</g,"><").trim()

    if(inserirVersaoXml)
      xml = '<?xml version="1.0"?>\n' + xml



    let sig = new SignedXml({
      idAttribute: 'Id'
    })
    sig.addReference(referencia, ['http://www.w3.org/2000/09/xmldsig#enveloped-signature', 'http://www.w3.org/TR/2001/REC-xml-c14n-20010315'],
      "", "", "", "", false)

    let arquivo = fs.readFileSync(this.caminhoCertificado)
    sig.signingKey = arquivo

    sig.keyInfoProvider = new MyKeyInfo(this.caminhoCertificado)
    sig.canonicalizationAlgorithm = "http://www.w3.org/TR/2001/REC-xml-c14n-20010315"
    sig.computeSignature(xml,  localAssinatura)
    return sig.getSignedXml()

  }
}

class MyKeyInfo {
  constructor(private certificado: string = null) {


  }
  getKeyInfo(key: any, prefix: any) {
    prefix = prefix || ''
    prefix = prefix ? prefix + ':' : prefix

    let chaveEditada = key.toString().split('-----END PRIVATE KEY-----')[1].split('-----END CERTIFICATE-----')[0]

    let certificado = chaveEditada.replace(
      '-----BEGIN CERTIFICATE-----', '').trim().replace(
        '-----END CERTIFICATE-----', '').trim().replace(
          /(\r\n\t|\n|\r\t|\r\n|\r)/gm,"");


    return "<" + prefix + "X509Data><X509Certificate>" + certificado + "</X509Certificate></" + prefix + "X509Data>"
  }

  getKey (keyInfo: any) {
    //you can use the keyInfo parameter to extract the key in any way you want
    return keyInfo //fs.readFileSync(this.certificado)
  }
}
