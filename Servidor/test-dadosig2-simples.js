// Teste simples da rota dadosig2
// Execute com: node test-dadosig2-simples.js

const axios = require('axios');

const textoSimples = `AeLe Sushi
🥢 O Melhor da Comida Oriental em Gyn!
⏰ Almoço Sab (11:30 ás 14:00)
Jantar (18h às 22:30): Seg a Sáb.
⬇️ Conheça também nosso delivery. 🛵`;

async function testeSimples() {
  try {
    console.log('🚀 Testando rota /dadosig2 com texto simples...');
    console.log('Texto enviado:', textoSimples);
    
    const response = await axios.post('http://localhost:3000/api/leads/dadosig2', {
      texto: textoSimples
    });

    console.log('\n📋 Resposta completa:');
    console.log(JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.error('❌ Erro no teste:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Dados:', error.response.data);
    }
  }
}

// Executar teste
testeSimples();
