import { Injectable } from '@angular/core';
import { Observable, from, Subject, throwError, of } from 'rxjs';
import { catchError, tap, delay, retry, switchMap } from 'rxjs/operators';
import { FacebookAuthResponse, WhatsAppSignupResponse } from './whatsapp.interface';

declare const FB: any;

@Injectable({
  providedIn: 'root'
})
export class FacebookSdkService {
  private readonly FB_CONFIG = {
    appId: '1003567500405109',
    configId: '1931923120965507',
    version: 'v22.0'
  };

  private messageSubject = new Subject<WhatsAppSignupResponse>();
  public messages$ = this.messageSubject.asObservable();
  private sdkReady = false;
  private scriptLoaded = false;

  constructor() {
    console.log('[FacebookSdk] Inicializando serviço');
    this.loadFacebookSDK();
    this.setupMessageListener();
  }

  private loadFacebookSDK(): void {
    console.log('[FacebookSdk] Iniciando carregamento do SDK do Facebook');

    if (typeof FB !== 'undefined') {
      console.log('[FacebookSdk] SDK já está definido, configurando...');
      this.configureFacebookSDK();
      return;
    }

    // Verificar se já existe um script do Facebook
    const existingScript = document.getElementById('facebook-jssdk');
    if (existingScript) {
      console.log('[FacebookSdk] Script já existe, aguardando carregamento...');
      this.scriptLoaded = true;

      // Definir fbAsyncInit para quando o script terminar de carregar
      (window as any).fbAsyncInit = () => {
        console.log('[FacebookSdk] fbAsyncInit chamado (script existente)');
        this.configureFacebookSDK();
      };

      return;
    }

    // Se não existe, carrega o script
    console.log('[FacebookSdk] Injetando script do Facebook SDK');
    const script = document.createElement('script');
    script.id = 'facebook-jssdk';
    script.src = 'https://connect.facebook.net/pt_BR/sdk.js';
    script.async = true;
    script.defer = true;

    // Configurar o callback fbAsyncInit antes de adicionar o script
    (window as any).fbAsyncInit = () => {
      console.log('[FacebookSdk] fbAsyncInit chamado (novo script)');
      this.configureFacebookSDK();
    };

    document.head.appendChild(script);
    this.scriptLoaded = true;
    console.log('[FacebookSdk] Script adicionado ao DOM');
  }

  private configureFacebookSDK(): void {
    if (!FB) {
      console.error('[FacebookSdk] FB não está definido ainda');
      return;
    }

    console.log('[FacebookSdk] Configurando SDK do Facebook com appId:', this.FB_CONFIG.appId);

    try {
      FB.init({
        appId: this.FB_CONFIG.appId,
        autoLogAppEvents: true,
        xfbml: true,
        version: this.FB_CONFIG.version
      });

      console.log('[FacebookSdk] SDK do Facebook inicializado com sucesso');
      this.sdkReady = true;
    } catch (error) {
      console.error('[FacebookSdk] Erro ao inicializar SDK do Facebook:', error);
    }
  }

  private setupMessageListener(): void {
    console.log('[FacebookSdk] Configurando listener de mensagens');

    window.addEventListener('message', (event: MessageEvent) => {
      // Verificar origem da mensagem
      if (event.origin !== "https://www.facebook.com" &&
          event.origin !== "https://web.facebook.com") {
        return;
      }

      console.log('[FacebookSdk] Mensagem recebida de:', event.origin);

      try {
        const data = JSON.parse(event.data);
        console.log('[FacebookSdk] Dados da mensagem:', typeof data, JSON.stringify(data));

        if (data.type === 'WA_EMBEDDED_SIGNUP') {
          console.log('[FacebookSdk] Mensagem do WhatsApp Signup recebida:', JSON.stringify(data));
          this.messageSubject.next(data);
        }
      } catch (error) {
        // Ignora mensagens que não são JSON
      }
    });
  }

  private waitForSDK(): Observable<boolean> {
    // Se o SDK já está pronto, retorna imediatamente
    if (this.sdkReady && typeof FB !== 'undefined') {
      return of(true);
    }

    console.log('[FacebookSdk] Aguardando SDK ficar pronto');

    // Se o script não foi carregado ainda, tenta carregar
    if (!this.scriptLoaded) {
      this.loadFacebookSDK();
    }

    // Tenta por um tempo limitado
    return of(true).pipe(
      delay(200), // Aguarda 200ms entre tentativas
      tap(() => {
        if (typeof FB === 'undefined') {
          throw new Error('FB ainda não definido');
        }
      }),
      retry(25), // Tenta 25 vezes (total ~5 segundos)
      tap(() => {
        console.log('[FacebookSdk] FB está definido, SDK pronto');
        if (!this.sdkReady) {
          this.configureFacebookSDK();
        }
      }),
      catchError(error => {
        console.error('[FacebookSdk] Timeout aguardando SDK:', error);
        return throwError(() => new Error('Timeout ao aguardar SDK do Facebook'));
      })
    );
  }

  public launchWhatsAppSignup(): Observable<FacebookAuthResponse> {
    console.log('[FacebookSdk] Iniciando login do WhatsApp');

    return this.waitForSDK().pipe(
      tap(() => console.log('[FacebookSdk] SDK pronto, prosseguindo com login')),
      switchMap(() => this.performLogin()),
      catchError(error => {
        console.error('[FacebookSdk] Erro no processo de login:', error);
        return throwError(() => new Error('Falha ao conectar com o Facebook: ' + error.message));
      })
    );
  }

  private performLogin(): Observable<FacebookAuthResponse> {
    alert('[FacebookSdk] Configurando parâmetros de login');

    const loginParams = {
      config_id: this.FB_CONFIG.configId,
      response_type: 'code',
      override_default_response_type: true,
      redirect_uri: "https://fibo.promokit.com.br",
      extras: {
        setup: {},
        featureType: 'whatsapp_business_app_onboarding',
        sessionInfoVersion: '2'
      }
    };

    console.log('[FacebookSdk] Parâmetros de login:', JSON.stringify(loginParams));

    return from(new Promise<FacebookAuthResponse>((resolve, reject) => {
      try {
        FB.login((response: any) => {
          console.log('[FacebookSdk] Resposta do login recebida:', JSON.stringify(response));

          if (response && response.authResponse) {
            console.log('[FacebookSdk] Login Facebook bem sucedido. Auth code:',
              response.authResponse?.code ? `${response.authResponse.code.substring(0, 10)}...` : 'ausente');
            resolve(response);
          } else {
            console.error('[FacebookSdk] Login Facebook falhou:', JSON.stringify(response));
            reject(new Error('Usuário cancelou o login ou ocorreu um erro'));
          }
        }, loginParams);
      } catch (error) {
        console.error('[FacebookSdk] Erro ao chamar FB.login:', error);
        reject(error);
      }
    })).pipe(
      tap(response => console.log('[FacebookSdk] Resposta do login processada:', JSON.stringify(response)))
    );
  }
}
