<form [ngClass]="{'needs-validation': !frmConfigImpressao.submitted, 'was-validated': frmConfigImpressao.submitted}"
      novalidate #frmConfigImpressao="ngForm" (ngSubmit)="onSubmitConfigImpressao()">

  <h4>Configurações de Impressão</h4>
  <small class="text-muted">Configure como seus pedidos serão impressos</small>

  <div class="config-layout">
    <!-- Coluna Esquerda -->
    <div class="config-column">
      <!-- Formato de Impressão -->
      <div class="config-section">
        <div class="section-title">
          <i class="fe-file-text"></i>
          Formato de Impressão do Pedido
        </div>

        <div class="radio-group">
          <div class="radio-option">
            <input id="completo" name="layoutPedido" type="radio"
                   [(ngModel)]="empresa.configImpressao.layoutPedido"
                   value="PedidoCompleto" (change)="alterouLeiaute()"/>
            <label for="completo">
              Completo
              <small>Imprime todos os detalhes do pedido</small>
            </label>
          </div>

          <div class="radio-option">
            <input id="resumido" name="layoutPedido" type="radio"
                   [(ngModel)]="empresa.configImpressao.layoutPedido"
                   value="PedidoResumido" (change)="alterouLeiaute()"/>
            <label for="resumido">
              Resumido
              <small>Imprime versão condensada do pedido</small>
            </label>
          </div>

          <div class="radio-option">
            <input id="ambos" name="layoutPedido" type="radio"
                   [(ngModel)]="empresa.configImpressao.layoutPedido"
                   value="Ambos" (change)="alterouLeiaute()"/>
            <label for="ambos">
              Ambos
              <small>Imprime as duas versões</small>
            </label>
          </div>
        </div>
      </div>

      <!-- Impressão Automática -->
      <div class="config-section">
        <div class="section-title">
          <i class="fe-clock"></i>
          Impressão automática ocorre quando?
        </div>

        <div class="radio-group">
          <div class="radio-option">
            <input id="mudancaStatus" name="momentoImprimirAuto" type="radio"
                   [(ngModel)]="empresa.configImpressao.momentoImprimirAuto"
                   value="status-pedido" (change)="marqueRecarregar()"/>
            <label for="mudancaStatus">
              Mudança de status do pedido
              <small>Imprimir quando o status for alterado</small>
            </label>
          </div>

          <div class="radio-option">
            <input id="novoPedido" name="momentoImprimirAuto" type="radio"
                   [(ngModel)]="empresa.configImpressao.momentoImprimirAuto"
                   value="novo-pedido" (change)="marqueRecarregar()"/>
            <label for="novoPedido">
              Quando um novo pedido for feito
              <small>Imprimir imediatamente após receber o pedido</small>
            </label>
          </div>
        </div>
      </div>

      <!-- Permitir impressão de pedidos não pagos -->
      <div class="config-section">
        <div class="section-title">
          <i class="fe-dollar-sign"></i>
          Permitir a impressão de pedidos online não pagos?
        </div>

        <div class="radio-group">
          <div class="radio-option">
            <input id="sim" name="imprimirOnlineNaoPago" type="radio"
                   [(ngModel)]="empresa.configImpressao.imprimirOnlineNaoPago"
                   [value]="true" (change)="marqueRecarregar()"/>
            <label for="sim">Sim</label>
          </div>

          <div class="radio-option">
            <input id="nao" name="imprimirOnlineNaoPago" type="radio"
                   [(ngModel)]="empresa.configImpressao.imprimirOnlineNaoPago"
                   [value]="false" (change)="marqueRecarregar()"/>
            <label for="nao">Não</label>
          </div>
        </div>
      </div>
      <div class="input-group mt-0">
        <input  name="ocultarCobranca" id='ocultarCobranca'    class="k-checkbox  " type="checkbox" kendoCheckBox
                [(ngModel)]="empresa.configImpressao.ocultarCobranca"   />
        <label  class="k-checkbox-label" for="ocultarCobranca">
          Ocultar dados de cobrança em pedidos nao pagos ( mensagem cobrar do cliente e valor restante a pagar)</label>

      </div>

      <!-- Impressão nativa -->
      <div class="config-section" *ngIf="!isMobile">
        <div class="section-title">
          <i class="fe-printer"></i>
          Impressão nativa
        </div>

        <div *ngIf="conectando" class="alert alert-info">
          <i class="fe-loader"></i> Verificando QZ Tray...
        </div>

        <ng-container *ngIf="!this.conectando && !this.qzInstalado && !this.isMobile">
          <div class="form-group mb-4 col-12" >
            <label>Impressão nativa</label><br>
            <div class="qz-error-notification">
              <div class="notification-content">
                <i class="fas fa-print notification-icon"></i>
                <div class="notification-message">
                  <strong>Impressora não conectada</strong>
                  <p>Verifique se o QZ Tray está em execução no seu computador</p>
                </div>
              </div>
              <div class="notification-actions">
                <button (click)="tenteConectarQZ()" class="btn btn-warning waves-effect waves-light mr-2" [disabled]="salvando">
                  <i class="fas fa-sync fa-fw"></i>
                  Tentar novamente
                </button>
                <button (click)="abraQZTray()" class="btn btn-primary waves-effect waves-light" [disabled]="salvando" type="button">
                  <i class="fas fa-external-link-alt fa-fw"></i>
                  Abrir QZ Tray
                </button>
              </div>
            </div>
          </div>
        </ng-container>
        <ng-container *ngIf="!conectando && qzInstalado">
          <div class="checkbox-group">
            <label class="checkbox">
              <input type="checkbox" [(ngModel)]="empresa.configImpressao.imprimirTXT"
                     name="imprimirTXT" (change)="alterouTipoDeImpressao()">
              Realizar impressão em modo nativo
              <small>Impressão rápida e direta</small>
            </label>

            <label class="checkbox" *ngIf="empresa.configImpressao.imprimirTXT">
              <input type="checkbox" [(ngModel)]="empresa.configImpressao.modoHTML"
                     name="modoHTML">
              Utilizar o modo gráfico
              <small>*necessário driver nativo da impressora</small>
            </label>

            <label class="checkbox" *ngIf="empresa.configImpressao.imprimirTXT && empresa.configImpressao.modoHTML">
              <input type="checkbox" [(ngModel)]="empresa.configImpressao.cortarAutomatico"
                     name="cortarAutomatico">
              Enviar comando de corte ao fim da impressão
              <small>*use apenas se a impressora não cortar automático </small>
            </label>

            <label class="checkbox" *ngIf="empresa.configImpressao.imprimirTXT">
              <input type="checkbox" [(ngModel)]="empresa.configImpressao.emitirBeep"
                     name="emitirBeep">
              Emitir um beep após impressão
              <small>Alerta sonoro em impressoras compatíveis</small>
            </label>
          </div>
        </ng-container>
      </div>
    </div>

    <!-- Coluna Direita -->
    <div class="config-column">
      <div class="config-section">
        <div class="section-header">
          <div class="section-title">
            <i class="fe-printer"></i>
            Impressoras Cadastradas
          </div>
          <button type="button" class="btn-add" (click)="adicionarNovaImpressa()"
                  *ngIf="empresa.configImpressao.multiplasImpressoras">
            + Adicionar impressora
          </button>
        </div>
        <small class="text-muted">Gerencie suas impressoras cadastradas no sistema</small>

        <div class="impressoras-list">
          <div class="impressora-item" *ngFor="let impressora of empresa.configImpressao.impressoras; let i = index"
               [class.removed]="impressora.removida">
            <div class="item-content">
              <i class="fe-printer"></i>
              <div class="item-info">
                <strong>{{impressora.setor || 'Sem setor'}}</strong>
                <span>{{impressora.nome || 'Selecione uma impressora'}}</span>
                <div class="item-configs">
                  <span class="config-tag" *ngIf="impressora.imprimirAutomatico">Auto</span>
                  <span class="config-tag" *ngIf="impressora.imprimirNFCe">NFCe</span>
                  <span class="config-tag" *ngIf="impressora.naoImprimirMesa">Não Mesa</span>
                  <span class="config-tag" *ngIf="impressora.naoImprimirDelivery">Não Delivery</span>
                  <span class="config-tag">{{impressora.layout || 'Layout padrão'}}</span>
                  <span class="config-tag">{{impressora.tamanhoPapel || 'Papel padrão'}}</span>
                </div>
              </div>
            </div>
            <div class="item-actions">
              <button type="button" class="btn-icon" (click)="abrirConfigImpressora(impressora, i)">
                <i class="fe-settings"></i>
              </button>
              <button type="button" class="btn-icon text-danger" (click)="excluirImpressora(impressora)">
                <i class="fe-trash-2"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="alert alert-success" *ngIf="mensagemSucesso">
    <i class="fe-check-circle"></i> {{mensagemSucesso}}
  </div>

  <div class="alert alert-danger" *ngIf="erro">
    <i class="fe-alert-circle"></i> {{erro}}
  </div>

  <div class="alert alert-warning" *ngIf="impressorasAlteradas">
    <i class="fe-alert-triangle"></i>
    Existem alterações nas impressoras que ainda não foram salvas. Clique em "Salvar Configurações" para aplicar as mudanças.
  </div>

  <button type="submit" class="btn-save" [disabled]="salvando">
    <i class="fe-save"></i>
    Salvar Configurações
  </button>
</form>

<!-- Modal de Configuração -->
<kendo-dialog *ngIf="impressoraSelecionada"
              [title]="'Configurar Impressora'"
              (close)="fecharConfigImpressora()"
              [minWidth]="400"
              [width]="600">
  <div class="config-impressora">
    <div class="form-row">
      <div class="form-group">
        <label>Setor</label>
        <input type="text" [(ngModel)]="impressoraSelecionada.setor"
               [name]="'setor'+indexSelecionado"
               placeholder="Ex: CAIXA, COZINHA">
      </div>

      <div class="form-group">
        <label>Impressora</label>
        <kendo-dropdownlist [name]="'impressora'+indexSelecionado"
                          [(ngModel)]="impressoraSelecionada.nome"
                          [data]="impressoras"
                          (valueChange)="onImpressoraNomeChange()"
                          placeholder="Selecione uma impressora">
        </kendo-dropdownlist>
      </div>
    </div>

    <div class="form-row">
      <div class="form-group">
        <label>Layout</label>
        <kendo-dropdownlist [name]="'layout'+indexSelecionado"
                          [(ngModel)]="impressoraSelecionada.layout"
                          [data]="layouts"
                          placeholder="Escolha o layout">
        </kendo-dropdownlist>
      </div>

      <div class="form-group">
        <label>Papel</label>
        <kendo-dropdownlist [name]="'papel'+indexSelecionado"
                          [(ngModel)]="impressoraSelecionada.tamanhoPapel"
                          [data]="listaTamanhosPapeis"
                          placeholder="Tamanho do papel">
        </kendo-dropdownlist>
      </div>
    </div>

    <div class="checkbox-group">
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="impressoraSelecionada.imprimirAutomatico"
               [name]="'auto'+indexSelecionado">
        <div>
          Imprimir automaticamente
          <small>Os pedidos serão impressos sem intervenção manual</small>
        </div>
      </label>

      <label class="checkbox" *ngIf="possuiModuloFiscal">
        <input type="checkbox" [(ngModel)]="impressoraSelecionada.imprimirNFCe"
               [name]="'nfce'+indexSelecionado">
        <div>
          Imprimir NFCe
          <small>Notas fiscais serão impressas nesta impressora</small>
        </div>
      </label>

      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="impressoraSelecionada.naoImprimirMesa"
               [name]="'mesa'+indexSelecionado">
        <div>
          Não imprimir mesa
          <small>Pedidos de mesa não serão impressos</small>
        </div>
      </label>

      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="impressoraSelecionada.naoImprimirDelivery"
               [name]="'delivery'+indexSelecionado">
        <div>
          Não imprimir delivery
          <small>Pedidos de delivery não serão impressos</small>
        </div>
      </label>
    </div>

    <div class="comandos-fim">
      <label>Comandos de fim</label>
      <input type="text" [(ngModel)]="impressoraSelecionada.comandosFimImpressao"
             [name]="'comandos'+indexSelecionado"
             placeholder="\x1B\x69">
      <small>Comandos especiais enviados ao final da impressão</small>
    </div>
  </div>

  <kendo-dialog-actions>
    <button type="button" class="btn-secondary" (click)="fecharConfigImpressora()">Cancelar</button>
    <button type="button" class="btn-primary" (click)="salvarConfigImpressora()">Salvar</button>
  </kendo-dialog-actions>
</kendo-dialog>


