<div class="left-side-menu">
  <div class="nav-user">
    <a class="nav-link ">
      <img style="width:32px; height: 32px;" src="/assets/fidelidade/icones/user-icon-cinza.png" alt="user-image" class="float-left">
      <div class="dadosUsuario">
        <span class="ml-1"><b>{{usuario.nome}}</b></span><br>
        <span class="ml-1 empresa">{{usuario.empresa.nome}}</span>
      </div>
    </a>
  </div>

  <div class="">
    <!--- Sidemenu -->
    <div id="sidebar-menu">
      <ul class="metismenu" id="side-menu">
        <li>
          <a   (click)="naveguePara('empresas')" [ngClass]="{'text-primary text-bold':url.indexOf('empresas') != -1}" href="javascript: void(0);">
            <i class="far fa-building"></i>
            <span> Empresas</span>
          </a>
        </li>

        <li>
          <a   (click)="naveguePara('formaspagamento')" [ngClass]="{'text-primary text-bold':url.indexOf('formaspagamento') != -1}" href="javascript: void(0);">
            <i class="fa fa-credit-card"></i>
            <span> Formas de Pagamento</span>
          </a>
        </li>
        <li>
          <a   (click)="naveguePara('planos')" [ngClass]="{'text-primary text-bold':url.indexOf('planos') != -1}" href="javascript: void(0);">
            <i class="far fa-id-card"></i>
            <span> Planos</span>
          </a>
        </li>
        <li>
          <a   (click)="naveguePara('recebimentos')" [ngClass]="{'text-primary text-bold':url.indexOf('recebimentos') != -1}" href="javascript: void(0);">
            <i class="fa fa-file-invoice"></i>
            <span> Recebimento</span>
          </a>
        </li>
        <li>
          <a   (click)="naveguePara('grupos-de-lojas')" [ngClass]="{'text-primary text-bold':url.indexOf('grupos-de-lojas') > 0}" href="javascript: void(0);">
            <i class="fa fa-file-invoice"></i>
            <span> Grupos de Lojas</span>
          </a>
        </li>
        <li>
          <a   (click)="naveguePara('prompts-mia/global')" [ngClass]="{'text-primary text-bold':url.indexOf('prompts-mia') > 0}" href="javascript: void(0);">
            <i class="fas fa-robot"></i>
            <span> Prompts (Mia)</span>
          </a>
        </li>
        <li *ngIf="usuario.id < 3">
          <a    (click)="naveguePara('nfse')" [ngClass]="{'text-primary text-bold':url.indexOf('nfse') != -1}" href="javascript: void(0);">
            <i class="fa fa-file-invoice-dollar"></i>
            <span> Notas fiscais </span>
          </a>
        </li>
        <li>
        <li>
          <a   (click)="naveguePara('pedidosintegrados')" [ngClass]="{'text-primary text-bold':url.indexOf('pedidosintegrados') != -1}" href="javascript: void(0);">
            <i class="fe-shopping-cart"></i>
            <span> Pedidos Integrados</span>
          </a>
        </li>

        <li>
          <a   (click)="naveguePara('campanhas')" [ngClass]="{'text-primary text-bold':url.indexOf('campanhas') != -1}" href="javascript: void(0);">
            <i class="fa fa-file-invoice"></i>
            <span> Aprovações</span>
          </a>
        </li>
        <li>
          <a   (click)="naveguePara('leads')" [ngClass]="{'text-primary text-bold':url.indexOf('leads') != -1}" href="javascript: void(0);">
            <i class="fas fa-tags"></i>
            <span> Leads</span>
          </a>
        </li>
        <li>
          <a routerLink="naveguePara('ceps-customizados')" routerLinkActive="active" [ngClass]="{'text-primary text-bold':url.indexOf('ceps-customizados') != -1}" >
            <i class="fe-map-pin"></i>
            <span>Ceps Customizados</span>
          </a>
        </li>

      </ul>

    </div>
    <!-- End Sidebar -->

    <div class="clearfix"></div>
    <!--<button type="button" class="btn btn-primary width-lg mx-auto" style=" display: block;">Criar promoção</button>-->
  </div>


</div>


