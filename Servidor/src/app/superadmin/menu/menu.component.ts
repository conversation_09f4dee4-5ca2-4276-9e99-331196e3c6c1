import { Component, OnInit } from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {AutorizacaoService} from "../../services/autorizacao.service";
import { IntlService } from '@progress/kendo-angular-intl';

@Component({
  selector: 'app-menu',
  templateUrl: './menu.component.html',
  styleUrls: ['./menu.component.scss']
})
export class MenuComponent implements OnInit {
  public customCurrencyOptions = {
    style: 'currency',
    currency: 'BRL',
    currencyDisplay: 'displayName'
  };

  url = 'index';
  usuario: any;

  constructor(public intl: IntlService, private router: Router, private route: ActivatedRoute,
              private autorizacaoService: AutorizacaoService ) {
    route.url.subscribe(() => {
      this.url = route.snapshot.firstChild.routeConfig.path;
    });
  }

  ngOnInit() {
    let usuarioLogado = this.autorizacaoService.getUser();

    if( usuarioLogado != null )
      this.usuario = usuarioLogado;
    else {
      this.usuario = {};
    }
  }

  naveguePara( url: string ) {
    this.url = url;
    this.router.navigateByUrl( '/superadmin/' + url);
  }
}
