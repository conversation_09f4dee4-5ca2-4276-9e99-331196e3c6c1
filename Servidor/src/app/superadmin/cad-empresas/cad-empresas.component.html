<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <div class="page-title-right">
          <ol class="breadcrumb m-0">
            <li class="breadcrumb-item"><a routerLink="/superadmin/empresas">Empresas</a></li>
            <li class="breadcrumb-item active">{{empresa.nome}}</li>
          </ol>
        </div>
        <h4 class="page-title">
          <button class="btn btn-outline-blue btn-rounded" (click)="voltar()">
            <i class="fa fa-arrow-left ct-point" ></i>
          </button>&nbsp;&nbsp; <i class="far fa-building"></i>
          <span *ngIf="!empresa?.id"> Nova Empresa </span>
          <span *ngIf="empresa?.id"> {{empresa.nome}} </span>
        </h4>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col">
      <div class="card">
        <div class="card-body">
          <div class="alert alert-success alert-dismissible fade show mt-2" *ngIf="mensagemSucesso" role="alert">
            <i class="mdi mdi-check-all mr-2"></i> {{mensagemSucesso}}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close" (click)="fecheMensagemSucesso()">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>

          <kendo-tabstrip class="nav-bordered">
            <kendo-tabstrip-tab [title]="'Básicos'" [selected]="true">
              <ng-template kendoTabContent>
                <form [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"  novalidate #frm="ngForm" (ngSubmit)="onSubmit()">
                  <div class="row">
                    <div class="form-group col-6">
                      <label for="nome">Nome da Empresa</label>
                      <input type="text" class="form-control" autocomplete="off"
                             id="nome" name="nome" [(ngModel)]="empresa.nome" #nome="ngModel"
                             placeholder="Nome da Empresa" required>
                      <div class="invalid-feedback">
                        <p *ngIf="nome.errors?.required">Nome é obrigatório</p>
                      </div>
                    </div>
                    
                    <div class="form-group col-6">
                      <label for="razaoSocial">Razão Social</label>
                      <input type="text" class="form-control" autocomplete="off"
                             id="razaoSocial" name="razaoSocial" [(ngModel)]="empresa.razaoSocial" #razaoSocial="ngModel"
                             placeholder="Razão Social da Empresa">
                      <div class="invalid-feedback">
                        <p *ngIf="razaoSocial.errors?.required">Razão Social é obrigatória</p>
                      </div>
                    </div>
                  </div>

                  <div class="row">
                    <div class="form-group mb-3 col-3">
                      <label for="dominio">Subdomínio ([subdominio].meucardapio.ai) </label>
                      <input type="text" class="form-control" autocomplete="off"
                             id="dominio" name="dominio" [(ngModel)]="empresa.dominio" #dominio="ngModel"
                             placeholder="Domínio" value="" required>
                      <div class="invalid-feedback">

                        <p *ngIf="dominio.errors?.required">Subdomínio é obrigatório</p>
                      </div>
                    </div>
                    <div class="form-group mb-3 col-2" *ngIf="empresa.id">
                      <label for="whatsapp">Whatsapp Principal</label>
                      <div id="whatsapp">
                        <span class="font-14"><strong>{{empresa.numeroWhatsapp?.whatsapp | telefone}}</strong></span>
                        <button class="btn btn-sm btn-primary ml-2" (click)="abrirModalNumerosWhatsapp()">Ver Números</button>
                      </div>
                    </div>
                    <div class="form-group mb-3 col-3" *ngIf="empresa.id">
                      <label for="whatsapp">Domínio próprio</label>
                      <div id="dominioProprio">
                        <ng-container *ngIf="empresa.urlDaEmpresa">
                          <span>
                            <a href="https://{{empresa.urlDaEmpresa.hostname}}" target="_blank">
                              {{empresa.urlDaEmpresa.hostname}}</a></span>
                          <button class="btn btn-sm btn-danger ml-2" (click)="desvincularDominio()">Desvincular domínio</button>
                        </ng-container>
                        <ng-container *ngIf="!empresa.urlDaEmpresa">
                          <button class="btn btn-sm btn-primary" (click)="vincularDominio()">Vincular domínio</button>
                        </ng-container>

                      </div>
                    </div>

                    <div class="form-group mb-3 col-4">
                      <label for="cnpj">CNPJ</label>
                      <input   type="text" class="form-control" autocomplete="off" cnpjValido  mask="00.000.000/0000-00"
                               id="cnpj" name="cnpj" [(ngModel)]="empresa.cnpj" #cnpj="ngModel"
                               placeholder="CNPJ da Empresa"    />
                      <div class="invalid-feedback">
                        <p *ngIf="cnpj.errors?.required">CNPJ é obrigatório</p>
                        <p *ngIf="cnpj.errors?.cnpjInvalido">CNPJ inválido</p>
                      </div>
                    </div>

                    <div class="form-group mb-3 col-2 ">
                      <label for="email">E-mail cobrança</label>
                      <input type="email" class="form-control" autocomplete="off"
                             id="email" name="email" [(ngModel)]="empresa.email" #email="ngModel"
                             placeholder="Email da empresa"       >
                    </div>

                    <div class="form-group mb-3 col-2">
                      <label for="rede">Rede</label>

                      <kendo-dropdownlist id="rede" name="rede" [(ngModel)]="empresa.objRede" [data]="redes" (ngModelChange)="alterouRede()"
                                          placehoder="Selecione um responsavel" class="form-control" textField="nome"
                                          >

                      </kendo-dropdownlist>
                    </div>



                    <div class="form-group mb-3 col-4">
                      <label for="responsavel">Responsável</label>

                      <kendo-dropdownlist id="responsavel" name="responsavel" [(ngModel)]="empresa.responsavel" [data]="usuarios" (click)="carregueResponsaveis()"
                                          placeholder="Selecione um responsavel" class="form-control" textField="nome"
                                          >

                      </kendo-dropdownlist>
                    </div>

                    <div class="form-group mb-3 col-4 cep">
                      <label for="nome">CEP</label>
                      <div class="" style="position: relative;">
                        <kendo-maskedtextbox    (change)="alterou($event)"  [disabled]="buscandoCEP"
                                                type="text" class="form-control" autocomplete="off"
                                                id="cep" name="cep" [(ngModel)]="empresa.cep" #nome="ngModel"
                                                placeholder="Informe o CEP."   mask="00.000-000" ></kendo-maskedtextbox>

                        <i class="k-icon k-i-loading" *ngIf="buscandoCEP"></i>
                      </div>
                    </div>

                    <div class="form-group mb-3 col-8 cep">
                      <label for="nome">Descrição do Endereço (Substituirá o endereço do cliente)</label>
                      <div class="" style="position: relative;">
                        <input type="text" type="text" class="form-control" autocomplete="off"
                                                id="descricaoEndereco" name="descricaoEndereco" [(ngModel)]="empresa.descricaoEndereco" #nome="ngModel"
                                                placeholder="Descrição do Endereço - Se preenchido será exibir no lugar do endereço da loja" />
                      </div>
                    </div>

                    <div class="form-group mb-3 col-6" *ngIf="!empresa.enderecoCompleto">
                      <label for="nome">Endereço</label>
                      <input type="text" class="form-control" autocomplete="off"
                             id="endereco" name="endereco" [(ngModel)]="empresa.endereco" #endereco="ngModel"
                             placeholder="Endereço da Empresa" value=""  >
                      <div class="invalid-feedback">
                        <p *ngIf="endereco.errors?.required">Endereço é obrigatório</p>
                      </div>
                    </div>
                    <div class="form-group mb-3 col-2" *ngIf="!empresa.enderecoCompleto">
                      <label for="nome">Posição Geográfica</label>
                      <input type="text" class="form-control" autocomplete="off"
                              name="localizacao" [(ngModel)]="empresa.latitudeLongitude" #localizacao="ngModel"
                             placeholder="Sem Localização" value="" disabled="true"
                             style="color: #fff;"
                             [style.background-color]="empresa.latitudeLongitude ? '#ccc' : '#e03f3f'">
                    </div>


                  </div>

                  <div class="row">
                    <div class="form-group mb-3 col-3">
                      <label for="nome">Logradouro</label>
                      <input type="text" class="form-control" autocomplete="off"
                             name="logradouro" [(ngModel)]="empresa.enderecoCompleto.logradouro" #logradouro="ngModel"
                             placeholder="Logradouro" required   [disabled]="buscandoCEP">
                      <div class="invalid-feedback">
                        <p *ngIf="logradouro.errors?.required">Logradouro é obrigatório</p>
                      </div>
                    </div>
                    <div class="form-group mb-3 col-3">
                      <label for="nome">Numero</label>
                      <input type="text" class="form-control" autocomplete="off"
                             name="numero" [(ngModel)]="empresa.enderecoCompleto.numero" #numero="ngModel"
                             placeholder="Número" [required]="!empresa.enderecoCompleto.complemento"   [disabled]="buscandoCEP">
                      <div class="invalid-feedback">
                        <p *ngIf="numero.errors?.required">Número ou complemento é obrigatório</p>
                      </div>
                    </div>
                    <div class="form-group mb-3 col-3">
                      <label for="nome">Complemento</label>
                      <input type="text" class="form-control" autocomplete="off"
                             name="complemento" [(ngModel)]="empresa.enderecoCompleto.complemento" #complemento="ngModel"
                             placeholder="Complemento" [required]="!empresa.enderecoCompleto.numero"   [disabled]="buscandoCEP">
                      <div class="invalid-feedback">
                        <p *ngIf="complemento.errors?.required">Complemento ou número é obrigatório</p>
                      </div>
                    </div>

                    <div class="form-group mb-3 col-3">
                      <label for="nome">Bairro</label>
                      <input type="text" class="form-control" autocomplete="off"
                             name="bairro" [(ngModel)]="empresa.enderecoCompleto.bairro" #bairro="ngModel"
                             placeholder="Bairro" required  [disabled]="buscandoCEP" >
                      <div class="invalid-feedback">
                        <p *ngIf="bairro.errors?.required">Bairro é obrigatório</p>
                      </div>
                    </div>

                    <div class="form-group mb-3 col-3">
                      <label for="nome">Localidade</label>
                      <input type="text" class="form-control" autocomplete="off"
                             name="localidade" [(ngModel)]="empresa.enderecoCompleto.localidade" #localidade="ngModel"
                             placeholder="Cidade e UF" required  [disabled]="buscandoCEP"  [disabled]="true">
                      <div class="invalid-feedback">
                        <p *ngIf="localidade.errors?.required">Localidade é obrigatório</p>
                      </div>
                    </div>

                    <div class="form-group mb-3 col-3">
                      <label for="nome">Posição Geográfica</label>
                      <input type="text" class="form-control" autocomplete="off"
                              name="localizacao" [(ngModel)]="empresa.latitudeLongitude"
                             placeholder="Sem Localização" value="" disabled="true"
                             style="color: #fff;"
                             [style.background-color]="empresa.latitudeLongitude ? '#ccc' : '#e03f3f'">
                    </div>
                  </div>

                  <div class="row">
                    <div class="form-group mb-3 col-5">
                      <label for="nome">Descrição</label>
                      <textarea kendoTextArea type="text" class="form-control" autocomplete="off" style="height: 100px;"
                                id="descricao" name="descricao" [(ngModel)]="empresa.descricao" #descricao="ngModel"
                                placeholder="Descrição da Empresa" value="" required></textarea>
                      <div class="invalid-feedback">
                        <p *ngIf="nome.errors?.required">Descrição é obrigatório</p>

                      </div>
                    </div>
                    <div class="form-group mb-3 col-6">
                      <label for="nome">Link Maps</label>
                      <div class="input-group">
                        <div class="input-group-prepend">
                          <span class="input-group-text" id="basic-addon2">@</span>
                        </div>
                        <input type="text" class="form-control" autocomplete="off"
                               id="linkMaps" name="linkMaps" [(ngModel)]="empresa.linkMaps" #linkMaps="ngModel"
                               placeholder="Link Maps" >
                        <div class="invalid-feedback">
                          <p *ngIf="linkMaps.errors?.required">Link do Maps é obrigatório</p>
                        </div>
                      </div>
                    </div>
                    <div class="form-group mb-3 col-6">
                      <label for="nome">Titulo Fotos</label>
                      <input type="text" class="form-control" autocomplete="off"
                             id="tituloDestaques" name="tituloDestaques" [(ngModel)]="empresa.tituloDestaques" #tituloDestaques="ngModel"
                             placeholder="Titulos do destaque" value=""  >

                    </div>
                    <div class="form-group mb-3 col-6">
                      <label for="nome">Titulo Destaques</label>
                      <input type="text" class="form-control" autocomplete="off"
                             id="tituloFotos" name="tituloFotos" [(ngModel)]="empresa.tituloFotos" #tituloFotos="ngModel"
                             placeholder="Títulos das fotos" value=""  >

                    </div>
                    <div class="form-group mb-4 col-4">
                      <label for="nome">Instagram</label>
                      <div class="input-group">
                        <div class="input-group-prepend">
                          <span class="input-group-text"  >@</span>
                        </div>
                        <input type="text" class="form-control" autocomplete="off"
                               id="instagram" name="instagram" [(ngModel)]="empresa.instagram" #instagram="ngModel"
                               placeholder="Instagram" value=""  >

                      </div>
                    </div>

                    <div class="form-group mb-4 col-4">
                      <label for="nome">Facebook</label>
                      <div class="input-group">
                        <div class="input-group-prepend">
                          <span class="input-group-text"  >https://facebook.com/</span>
                        </div>
                        <input type="text" class="form-control" autocomplete="off"
                               id="facebook" name="facebook" [(ngModel)]="empresa.facebook" #facebook="ngModel"
                               placeholder="Facebook" value="">
                      </div>
                    </div>

                    <div class="form-group mb-4 col-4">
                        <label for="nome">Nome Categoria Destaques</label>
                        <input type="text" class="form-control" autocomplete="off"
                               id="nomeCategoriaDestaques" name="nomeCategoriaDestaques" [(ngModel)]="empresa.nomeCategoriaDestaques" #instagram="ngModel"
                               placeholder="Nome Categoria Destaques" value="">
                    </div>
                  </div>

                  <button type="submit" class="btn btn-primary waves-effect waves-light" [disabled]="salvando">Salvar</button>
                </form>
              </ng-template>
            </kendo-tabstrip-tab>

              <kendo-tabstrip-tab [title]="'Contrato'" *ngIf="empresa.id">
                <ng-template kendoTabContent>
                    <app-tela-contrato [empresa]="empresa"></app-tela-contrato>
                </ng-template>
              </kendo-tabstrip-tab>

              <kendo-tabstrip-tab [title]="'Horários'" *ngIf="empresa.id">
                <ng-template kendoTabContent>
                   <app-cad-horariosfuncionamento [empresa]="empresa"></app-cad-horariosfuncionamento>
                </ng-template>
              </kendo-tabstrip-tab>

              <kendo-tabstrip-tab [title]="'Fidelidade'" *ngIf="empresa.id">
                <ng-template kendoTabContent>
                  <kendo-tabstrip class="nav-bordered"  >
                    <kendo-tabstrip-tab [title]="'Planos'" [selected]="true">
                      <ng-template kendoTabContent>
                        <app-planos [empresa]="empresa"></app-planos>
                      </ng-template>
                    </kendo-tabstrip-tab>

                    <kendo-tabstrip-tab [title]="'Atividades'">
                      <ng-template kendoTabContent>
                        <app-atividades [empresa]="empresa"></app-atividades>
                      </ng-template>
                    </kendo-tabstrip-tab>

                    <kendo-tabstrip-tab [title]="'Brindes'">
                      <ng-template kendoTabContent>
                        <app-brindes [empresa]="empresa"></app-brindes>
                      </ng-template>
                    </kendo-tabstrip-tab>
                  </kendo-tabstrip>

                </ng-template>
              </kendo-tabstrip-tab>

              <kendo-tabstrip-tab [title]="'Fotos'" *ngIf="empresa.id">
                <ng-template kendoTabContent>
                  <app-fotos   [empresa]="empresa"></app-fotos>
                </ng-template>
              </kendo-tabstrip-tab>
              <kendo-tabstrip-tab [title]="'Usuários'" *ngIf="empresa.id">
                <ng-template kendoTabContent>
                  <app-usuarios-empresa   [empresa]="empresa"></app-usuarios-empresa>
                </ng-template>
              </kendo-tabstrip-tab>
              <kendo-tabstrip-tab [title]="'Config'" *ngIf="empresa.id">
                <ng-template kendoTabContent>
                  <app-tela-configuracoes   [empresa]="empresa" [usuario]="usuario"></app-tela-configuracoes>
                </ng-template>
              </kendo-tabstrip-tab>
              <kendo-tabstrip-tab [title]="'Importar'" *ngIf="empresa.id">
                <ng-template kendoTabContent>
                  <app-tela-importar-contatos   [empresa]="empresa"></app-tela-importar-contatos>
                </ng-template>
              </kendo-tabstrip-tab>
            <kendo-tabstrip-tab [title]="'Histórico'" *ngIf="empresa.id">
              <ng-template kendoTabContent>
                <app-historico-operacoes   [empresa]="empresa"></app-historico-operacoes>
              </ng-template>
            </kendo-tabstrip-tab>

          </kendo-tabstrip>
        </div>
      </div>
  </div>
  </div>
</div>
<kendo-dialog title="Vincular Domínio" *ngIf="abraDialogVincularDominio" (close)="fecharDialogVincularDominio()" [minWidth]="250" [width]="450">
  <form [ngClass]="{'needs-validation': !frmVincularDominio.submitted, 'was-validated': frmVincularDominio.submitted}"  novalidate #frmVincularDominio="ngForm" (ngSubmit)="efetuarVinculoDominio(frmVincularDominio)">
    <div class="row">
      <div class="form-group mb-3 col-8" [ngClass]="{'col-12': !empresa?.integracaoDelivery}">
        <label for="nome">Informe a URL que deseja vincular à empresa:</label>
        <input type="text" class="form-control" autocomplete="off"
               id="novaUrlEmpresa" name="novaUrlEmpresa" [(ngModel)]="urlDaEmpresa.hostname" #novaUrlEmpresa="ngModel"
               placeholder="Ex: www.empresa.com.br" value="" required appAutoFocus [autoFocus]="true">
        <div class="invalid-feedback">
          <p *ngIf="novaUrlEmpresa.errors?.required">URL é obrigatoria</p>
        </div>
      </div>
    </div>

    <div class="modal-footer">
      <button type="submit" class="btn btn-primary waves-effect waves-light" >Salvar</button>
      <button type="button" (click)="fecharDialogVincularDominio()" kendoButton [primary]="true"  class="btn btn-light waves-effect">Cancelar</button>
    </div>

  </form>
</kendo-dialog>
