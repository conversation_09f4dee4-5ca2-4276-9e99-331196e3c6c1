import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ConversasService, MensagemChat, DadosLead, LinkLead } from '../services/conversas.service';
import { LeadService } from '../services/lead.service';

@Component({
  selector: 'app-crm-home',
  templateUrl: './crm-home.component.html',
  styleUrls: ['./crm-home.component.scss']
})
export class CrmHomeComponent implements OnInit {
  // Etapa do funil atual para teste
  etapaAtual = 'Conectado';

  // Dados do lead atual
  dadosLeadAtual: DadosLead | undefined;

  // Username do Instagram
  username: string | null = null;

  // Estado da busca
  carregandoLead = false;
  leadEncontrado = false;

  constructor(
    private conversasService: ConversasService,
    private route: ActivatedRoute,
    private router: Router,
    private leadService: LeadService
  ) {}

  ngOnInit(): void {
    // Captura o parâmetro username da rota
    this.route.paramMap.subscribe(params => {
      this.username = params.get('username');
      console.log('Username capturado da rota:', this.username);

      if (this.username) {
        this.buscarLeadPorUsername(this.username);
      } else {
        this.inicializarModoDemonstracao();
      }

      // Debug do estado após inicialização
      setTimeout(() => this.debugEstado(), 100);
    });

    // Subscreve para atualizações no contexto da conversa
    this.conversasService.contextoConversa$.subscribe(contexto => {
      if (contexto && contexto.dadosLead) {
        this.dadosLeadAtual = contexto.dadosLead;
      }
    });

  }

  /**
   * Simula o recebimento de novas mensagens para testar o componente de sugestões
   */
  simularNovasMensagens(): void {
    const horaAtual = new Date().toLocaleTimeString();

    // Alterna entre diferentes conjuntos de simulações de mensagens para testar
    // diferentes cenários e sugestões
    const cenarios = [
      // Cenário 1: Cliente interessado no preço
      {
        mensagens: [
          {
            texto: 'Olá, estou interessado no seu sistema de gestão. Quanto custa?',
            remetente: 'Cliente',
            horario: horaAtual,
            tipo: 'entrada' as const
          },
          {
            texto: 'Olá! Obrigado pelo interesse. Nossos planos começam a partir de R$99/mês.',
            remetente: 'Eu',
            horario: horaAtual,
            tipo: 'saida' as const
          },
          {
            texto: 'Isso parece interessante. Vocês oferecem alguma demonstração?',
            remetente: 'Cliente',
            horario: horaAtual,
            tipo: 'entrada' as const
          }
        ],
        contato: 'João Silva',
        telefone: '+5511987654321',
        etapa: 'Qualificação'
      },

      // Cenário 2: Cliente com objeção de preço
      {
        mensagens: [
          {
            texto: 'Obrigado pelas informações, mas achei um pouco caro para o meu negócio atual.',
            remetente: 'Cliente',
            horario: horaAtual,
            tipo: 'entrada' as const
          },
          {
            texto: 'Entendo sua preocupação com o investimento. Você já avaliou o retorno que pode ter?',
            remetente: 'Eu',
            horario: horaAtual,
            tipo: 'saida' as const
          },
          {
            texto: 'Ainda não calculei. Teria como me passar mais detalhes sobre isso?',
            remetente: 'Cliente',
            horario: horaAtual,
            tipo: 'entrada' as const
          }
        ],
        contato: 'Maria Oliveira',
        telefone: '+5511976543210',
        etapa: 'Objeção'
      },

      // Cenário 3: Cliente pronto para fechar
      {
        mensagens: [
          {
            texto: 'Fiquei muito satisfeito com a demonstração. Como fazemos para avançar?',
            remetente: 'Cliente',
            horario: horaAtual,
            tipo: 'entrada' as const
          },
          {
            texto: 'Ótimo! Posso enviar a proposta comercial e os próximos passos por e-mail.',
            remetente: 'Eu',
            horario: horaAtual,
            tipo: 'saida' as const
          },
          {
            texto: 'Perfeito. E quanto tempo leva para implementar o sistema?',
            remetente: 'Cliente',
            horario: horaAtual,
            tipo: 'entrada' as const
          }
        ],
        contato: 'Ricardo Mendes',
        telefone: '+5511955556666',
        etapa: 'Fechamento'
      }
    ];

    // Seleciona um cenário aleatoriamente
    const cenarioAtual = cenarios[Math.floor(Math.random() * cenarios.length)];

    // Cria dados de lead simulados para teste
    const dadosLeadSimulados = this.gerarDadosLeadSimulados(cenarioAtual.contato, cenarioAtual.etapa);

    // Atualiza o contexto da conversa com as mensagens simuladas e dados do lead
    this.conversasService.setContextoConversa({
      mensagens: cenarioAtual.mensagens,
      contatoAtual: cenarioAtual.contato,
      telefoneAtual: cenarioAtual.telefone,
      etapaFunil: cenarioAtual.etapa,
      dadosLead: dadosLeadSimulados
    });

    // Força a atualização local para garantir que os dados sejam exibidos
    this.dadosLeadAtual = dadosLeadSimulados;

    // Atualiza a etapa atual para o componente saber
    this.etapaAtual = cenarioAtual.etapa;
  }

  /**
   * Gera dados simulados de lead para testes
   */
  private gerarDadosLeadSimulados(nome: string, etapa: string): DadosLead {
    // Define diferentes perfis de lead baseados no nome
    const perfis = {
      'João Silva': {
        email: '<EMAIL>',
        cargo: 'Gerente de Operações',
        empresa: 'Pizzaria Bella Napoli',
        segmento: 'Alimentação',
        tamanhoEmpresa: 'Pequena' as const,
        localizacao: 'São Paulo, SP',
        instagram: '@bellanapoli_pizzaria',
        site: 'www.bellanapoli.com.br',
        dataPrimeiroContato: '10/05/2023',
        ultimaInteracao: new Date().toLocaleDateString(),
        origemLead: 'Site',
        scoreLead: 75,
        interessesProdutos: ['Cardápio Digital', 'Gestão de Pedidos'],
        proximoFollowUp: this.gerarDataFutura(3),
        historicoPropostas: 'Enviada proposta inicial em 15/05/2023',
        observacoes: 'Cliente demonstra interesse em automatizar atendimento',
        links: [
          { tipo: 'Ifood', url: 'https://www.ifood.com.br/delivery/sao-paulo-sp/pizzaria-bella-napoli', descricao: 'Cardápio no iFood', ordem: 1 },
          { tipo: 'Instagram', url: 'https://instagram.com/bellanapoli_pizzaria', descricao: 'Perfil no Instagram', ordem: 2 },
          { tipo: 'Localização', url: 'Rua das Flores, 123 - São Paulo, SP', descricao: 'Endereço da pizzaria', ordem: 3 }
        ]
      },
      'Maria Oliveira': {
        email: '<EMAIL>',
        cargo: 'Proprietária',
        empresa: 'Confeitaria Doce Sabor',
        segmento: 'Alimentação',
        tamanhoEmpresa: 'Pequena' as const,
        localizacao: 'Rio de Janeiro, RJ',
        instagram: '@docesabor_confeitaria',
        linkedin: 'linkedin.com/in/mariaoliveira',
        site: 'www.docesabor.com.br',
        dataPrimeiroContato: '22/04/2023',
        ultimaInteracao: new Date().toLocaleDateString(),
        origemLead: 'Instagram',
        scoreLead: 60,
        interessesProdutos: ['Sistema de Delivery', 'Fidelização'],
        proximoFollowUp: this.gerarDataFutura(2),
        observacoes: 'Preocupada com custo-benefício',
        links: [
          { tipo: 'Site', url: 'https://www.docesabor.com.br', descricao: 'Website oficial', ordem: 1 },
          { tipo: 'WhatsApp', url: '21987654321', descricao: 'WhatsApp para pedidos', ordem: 2 },
          { tipo: 'Instagram', url: 'https://instagram.com/docesabor_confeitaria', descricao: 'Instagram da confeitaria', ordem: 3 }
        ]
      },
      'Ricardo Mendes': {
        email: '<EMAIL>',
        cargo: 'Diretor',
        empresa: 'Restaurante Fusion',
        segmento: 'Alimentação',
        tamanhoEmpresa: 'Média' as const,
        localizacao: 'Belo Horizonte, MG',
        instagram: '@restaurantefusion',
        linkedin: 'linkedin.com/in/ricardomendes',
        site: 'www.restaurantefusion.com.br',
        dataPrimeiroContato: '03/06/2023',
        ultimaInteracao: new Date().toLocaleDateString(),
        origemLead: 'Indicação',
        scoreLead: 90,
        interessesProdutos: ['Sistema Completo', 'Integração PDV'],
        proximoFollowUp: this.gerarDataFutura(1),
        historicoPropostas: 'Apresentação realizada em 10/06/2023. Proposta enviada em 12/06/2023',
        observacoes: 'Cliente com alto potencial, já testou a solução concorrente',
        links: [
          { tipo: 'Site', url: 'https://www.restaurantefusion.com.br', descricao: 'Website do restaurante', ordem: 1 },
          { tipo: 'Ifood', url: 'https://www.ifood.com.br/delivery/belo-horizonte-mg/restaurante-fusion', descricao: 'Delivery no iFood', ordem: 2 },
          { tipo: 'Reservas', url: 'https://www.opentable.com.br/restaurante-fusion', descricao: 'Sistema de reservas', ordem: 3 },
          { tipo: 'Concorrente', url: 'https://sistema-concorrente.com.br', descricao: 'Sistema atual em uso', ordem: 4 }
        ]
      }
    };

    // Seleciona o perfil correspondente ou cria um genérico
    const perfil = perfis[nome] || {
      email: `${nome.toLowerCase().replace(' ', '.')}@email.com`,
      cargo: 'Proprietário',
      empresa: 'Empresa Exemplo',
      segmento: 'Alimentação',
      tamanhoEmpresa: 'Pequena' as const,
      dataPrimeiroContato: new Date().toLocaleDateString(),
      ultimaInteracao: new Date().toLocaleDateString(),
      origemLead: 'WhatsApp',
      scoreLead: 50
    };

    // Adiciona nome, telefone e etapa ao perfil
    return {
      ...perfil,
      nome: nome,
      telefone: this.gerarTelefoneAleatorio(),
      etapaFunil: etapa
    };
  }

  /**
   * Gera uma data futura para simulação
   */
  private gerarDataFutura(diasAFrente: number): string {
    const data = new Date();
    data.setDate(data.getDate() + diasAFrente);
    return data.toLocaleDateString();
  }

  /**
   * Gera um número de telefone aleatório para simulação
   */
  private gerarTelefoneAleatorio(): string {
    const ddd = Math.floor(Math.random() * 89) + 11; // DDD entre 11 e 99
    const parte1 = Math.floor(Math.random() * 9000) + 1000; // 4 dígitos
    const parte2 = Math.floor(Math.random() * 9000) + 1000; // 4 dígitos
    return `+55${ddd}9${parte1}${parte2}`;
  }


  /**
   * Retorna uma cor baseada no score do lead
   */
  getCorDoScore(score: number | undefined): string {
    if (!score) return '#999'; // Cinza para score indefinido

    if (score >= 80) return '#2ecc71'; // Verde para score alto
    if (score >= 50) return '#f39c12'; // Amarelo para score médio
    return '#e74c3c'; // Vermelho para score baixo
  }

  /**
   * Formata o score para exibição
   */
  formatarScore(score: number | undefined): string {
    if (!score && score !== 0) return 'N/A';
    return `${score}%`;
  }

  /**
   * Gera URL do WhatsApp
   */
  getWhatsAppUrl(telefone: string): string {
    if (!telefone) return '#';
    const numeroLimpo = telefone.replace(/\D/g, '');
    return `https://wa.me/55${numeroLimpo}`;
  }

  /**
   * Gera URL do Instagram
   */
  getInstagramUrl(instagram: string): string {
    if (!instagram) return '#';
    const username = instagram.replace('@', '');
    return `https://instagram.com/${username}`;
  }

  /**
   * Gera URL do website
   */
  getWebsiteUrl(site: string): string {
    if (!site) return '#';
    return site.startsWith('http') ? site : `https://${site}`;
  }

  /**
   * Obtém o ícone apropriado para cada tipo de link
   */
  getLinkIcon(tipo: string): string {
    const icones: { [key: string]: string } = {
      'Ifood': 'fa fa-utensils',
      'Site do Cardápio': 'fa fa-list-alt',
      'Concorrente': 'fa fa-exclamation-triangle',
      'Reservas': 'fa fa-calendar-check',
      'WhatsApp': 'fab fa-whatsapp',
      'Localização': 'fa fa-map-marker-alt',
      'Site': 'fa fa-globe',
      'Instagram': 'fa fa-instagram'
    };
    return icones[tipo] || 'fa fa-link';
  }

  /**
   * Obtém a cor apropriada para cada tipo de link
   */
  getLinkColor(tipo: string): string {
    const cores: { [key: string]: string } = {
      'Ifood': '#ea1d2c',
      'Site do Cardápio': '#007bff',
      'Concorrente': '#ff6b35',
      'Reservas': '#28a745',
      'WhatsApp': '#25d366',
      'Localização': '#dc3545',
      'Site': '#6c757d',
      'Instagram': '#e4405f'
    };
    return cores[tipo] || '#6c757d';
  }


  /**
   * Formata a URL do link baseado no tipo
   */
  getLinkUrl(link: LinkLead): string {
    if (!link.url) return '#';

    switch (link.tipo) {
      case 'WhatsApp':
        return this.formatarWhatsAppLink(link.url);
      case 'Localização':
        return this.formatarLocalizacaoLink(link.url);
      default:
        return link.url.startsWith('http') ? link.url : `https://${link.url}`;
    }
  }

  /**
   * Obtém o texto de exibição para o link
   */
  getLinkDisplayText(link: LinkLead): string {
    if (link.descricao) return link.descricao;

    switch (link.tipo) {
      case 'WhatsApp':
        return 'WhatsApp';
      case 'Localização':
        return 'Ver no Mapa';
      case 'Ifood':
        return 'Cardápio iFood';
      case 'Site do Cardápio':
        return 'Cardápio Online';
      case 'Concorrente':
        return 'Sistema Concorrente';
      case 'Reservas':
        return 'Fazer Reserva';
      default:
        return link.url;
    }
  }

  /**
   * Formata link do WhatsApp
   */
  private formatarWhatsAppLink(url: string): string {
    // Se já é um link do WhatsApp, retorna como está
    if (url.includes('wa.me') || url.includes('whatsapp.com')) {
      return url;
    }

    // Se é apenas um número, formata para link do WhatsApp
    const numeroLimpo = url.replace(/\D/g, '');
    if (numeroLimpo.length >= 10) {
      return `https://wa.me/55${numeroLimpo}`;
    }

    return url;
  }

  /**
   * Formata link de localização
   */
  private formatarLocalizacaoLink(url: string): string {
    // Se já é um link do Google Maps, retorna como está
    if (url.includes('maps.google.com') || url.includes('goo.gl/maps')) {
      return url;
    }

    // Se parece ser coordenadas ou endereço, formata para Google Maps
    if (url.includes(',') || url.includes('rua') || url.includes('av')) {
      return `https://maps.google.com/maps?q=${encodeURIComponent(url)}`;
    }

    return url;
  }

  /**
   * Busca lead na base de dados pelo username do Instagram
   */
  async buscarLeadPorUsername(username: string): Promise<void> {
    console.log('Iniciando busca para username:', username);
    this.carregandoLead = true;

    try {
      // Usa o LeadService para buscar por todos os leads com filtro por username
      const response = await this.leadService.liste({ 
        texto: username
      });
      
      console.log('Resposta da API via LeadService:', response);

      if (response && response.data && response.data.length > 0) {
        // Procura o lead exato pelo username do Instagram
        const lead = response.data.find((l: any) => 
          l.instagramHandle === username || 
          l.instagramHandle === username.replace('@', '')
        );

        if (lead) {
          // Lead encontrado - converter para formato DadosLead
          this.dadosLeadAtual = this.converterLeadParaDadosLead(lead);
          this.leadEncontrado = true;
          console.log('Lead encontrado e convertido:', this.dadosLeadAtual);

          // Atualizar contexto da conversa
          this.conversasService.setContextoConversa({
            mensagens: [],
            contatoAtual: lead.nomeResponsavel,
            telefoneAtual: lead.telefone,
            etapaFunil: lead.etapa,
            dadosLead: this.dadosLeadAtual
          });
        } else {
          // Username não encontrado na lista
          console.log('Username específico não encontrado na lista de leads');
          this.leadEncontrado = false;
          this.dadosLeadAtual = undefined;
        }
      } else {
        // Nenhum lead encontrado
        console.log('Nenhum lead encontrado na resposta da API');
        this.leadEncontrado = false;
        this.dadosLeadAtual = undefined;
      }
    } catch (error) {
      console.error('Erro ao buscar lead via LeadService:', error);
      this.leadEncontrado = false;
      this.dadosLeadAtual = undefined;
    } finally {
      this.carregandoLead = false;
      console.log('Estado final da busca - leadEncontrado:', this.leadEncontrado, 'carregandoLead:', this.carregandoLead);
      
      // Passo 1: Redirecionamento automático se lead não foi encontrado
      if (!this.leadEncontrado && this.username) {
        console.log('Lead não encontrado, redirecionando para novo lead');
        this.router.navigate(['/crm/novo-lead'], {
          queryParams: { username: this.username }
        });
      }
    }
  }

  /**
   * Converte objeto Lead do backend para DadosLead do frontend
   */
  private converterLeadParaDadosLead(lead: any): DadosLead {
    // Debug: Log da estrutura completa do lead para entender os dados
    console.log('CRM-HOME: Dados completos do lead recebido:', lead);
    console.log('CRM-HOME: Campo empresa:', lead.empresa);
    console.log('CRM-HOME: Campo crmEmpresa:', lead.crmEmpresa);
    console.log('CRM-HOME: Campos disponíveis:', Object.keys(lead));

    // Converter links se existirem
    let linksConvertidos: LinkLead[] = [];
    if (lead.links && Array.isArray(lead.links)) {
      linksConvertidos = lead.links.map((link: any) => ({
        tipo: link.tipo,
        url: link.url,
        descricao: link.descricao,
        ordem: link.ordem
      })).sort((a: LinkLead, b: LinkLead) => a.ordem - b.ordem);
    }

    // Determinar nome da empresa com fallbacks
    let nomeEmpresa = '';
    
    if (lead.empresa && lead.empresa.trim()) {
      nomeEmpresa = lead.empresa.trim();
      console.log('CRM-HOME: Usando lead.empresa:', nomeEmpresa);
    } else if (lead.crmEmpresa?.nome && lead.crmEmpresa.nome.trim()) {
      nomeEmpresa = lead.crmEmpresa.nome.trim();
      console.log('CRM-HOME: Usando lead.crmEmpresa.nome:', nomeEmpresa);
    } else if (lead.instagramHandle) {
      // Fallback: usar o Instagram handle formatado como empresa
      nomeEmpresa = lead.instagramHandle.replace('@', '').replace(/[_.-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
      console.log('CRM-HOME: Usando Instagram handle formatado como empresa:', nomeEmpresa);
    } else {
      nomeEmpresa = 'Empresa não informada';
      console.log('CRM-HOME: Nenhum nome de empresa encontrado, usando fallback');
    }

    const dadosConvertidos = {
      nome: lead.nomeResponsavel,
      telefone: lead.telefone,
      email: lead.crmEmpresa?.email || '',
      cargo: 'Proprietário',
      empresa: nomeEmpresa,
      segmento: lead.segmento || 'Alimentação',
      tamanhoEmpresa: 'Pequena' as const,
      localizacao: lead.instagramData?.location || '',
      instagram: lead.instagramHandle ? `@${lead.instagramHandle}` : '',
      site: lead.linkInsta || lead.instagramData?.website || '',
      dataPrimeiroContato: lead.dataCriacao ? new Date(lead.dataCriacao).toLocaleDateString() : '',
      ultimaInteracao: lead.dataUltimaInteracao ? new Date(lead.dataUltimaInteracao).toLocaleDateString() : new Date().toLocaleDateString(),
      origemLead: lead.origem,
      scoreLead: lead.score,
      etapaFunil: lead.etapa,
      interessesProdutos: [],
      proximoFollowUp: lead.dataProximoFollowup ? new Date(lead.dataProximoFollowup).toLocaleDateString() : '',
      observacoes: lead.observacoes || '',
      notas: lead.notas || '',
      links: linksConvertidos
    };

    console.log('CRM-HOME: Dados convertidos para exibição:', dadosConvertidos);
    return dadosConvertidos;
  }

  /**
   * Inicializa o modo demonstração quando não há username
   */
  private inicializarModoDemonstracao(): void {
    this.leadEncontrado = true;
    this.simularNovasMensagens();
  }


  /**
   * Método para debug - mostra o estado atual
   */
  debugEstado(): void {
    console.log('=== Estado atual do componente ===');
    console.log('username:', this.username);
    console.log('carregandoLead:', this.carregandoLead);
    console.log('leadEncontrado:', this.leadEncontrado);
    console.log('dadosLeadAtual:', this.dadosLeadAtual);
  }
}
