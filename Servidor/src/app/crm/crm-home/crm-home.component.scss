// ===== MODERN CRM HOME STYLES =====

.crm-home-container {
  min-height: 100vh;
  background: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

// ===== MODERN HEADER =====
.modern-header {
  background: #2563eb;
  color: white;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-brand {
    display: flex;
    align-items: center;
    gap: 15px;

    .brand-icon {
      width: 50px;
      height: 50px;
      background: #1d4ed8;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
    }

    .brand-text {
      .brand-title {
        margin: 0;
        font-size: 24px;
        font-weight: 700;
        color: white;
      }

      .brand-subtitle {
        font-size: 14px;
        color: white;
        opacity: 0.9;
      }
    }
  }

  .header-actions {
    .status-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      background: rgba(255, 255, 255, 0.1);
      padding: 8px 16px;
      border-radius: 20px;

      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #22c55e;

        &.active {
          animation: pulse 2s infinite;
        }
      }

      .status-text {
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

// ===== LOADING STATES =====
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;

  .loading-card, .loading-card.warning {
    background: white;
    border-radius: 16px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    max-width: 400px;

    &.warning {
      border-left: 4px solid #f59e0b;
    }

    .loading-animation {
      margin-bottom: 20px;

      .spinner-modern {
        width: 40px;
        height: 40px;
        border: 3px solid #f3f4f6;
        border-top: 3px solid #3b82f6;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }

    .warning-icon {
      font-size: 48px;
      color: #f59e0b;
      margin-bottom: 20px;
    }

    .loading-title {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 10px;
      color: #1f2937;
    }

    .loading-text {
      color: #6b7280;
      margin: 0;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// ===== LEAD DETAIL LAYOUT =====
.lead-detail-layout {
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px 20px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

// ===== HERO CARD =====
.hero-card {
  background: white;
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e5e7eb;

  .hero-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 32px;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 24px;
    }
  }

  .hero-main {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 24px;

    @media (max-width: 768px) {
      flex-direction: column;
      width: 100%;
    }
  }

  .company-info {
    .company-name {
      font-size: 32px;
      font-weight: 700;
      color: #1f2937;
      margin: 0 0 12px 0;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 28px;
      }
    }

    .contact-person {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #6b7280;
      font-size: 16px;

      i {
        color: #9ca3af;
      }
    }
  }

  .status-badges {
    display: flex;
    gap: 12px;
    flex-shrink: 0;

    @media (max-width: 768px) {
      justify-content: flex-start;
    }

    .score-badge, .stage-badge {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 8px 16px;
      border-radius: 20px;
      font-weight: 600;
      font-size: 14px;
      white-space: nowrap;
    }

    .score-badge {
      color: white;
      background: #059669;

      i {
        font-size: 12px;
      }
    }

    .stage-badge {
      background: #f3f4f6;
      color: #374151;

      &.stage-prospecção { background: #dbeafe; color: #1d4ed8; }
      &.stage-qualificação { background: #fef3c7; color: #92400e; }
      &.stage-objeção { background: #fed7d7; color: #c53030; }
      &.stage-fechamento { background: #d1fae5; color: #065f46; }
      &.stage-ganho { background: #dcfce7; color: #166534; }
      &.stage-perdido { background: #fee2e2; color: #dc2626; }
    }
  }

  .hero-actions {
    display: flex;
    gap: 12px;
    flex-shrink: 0;

    @media (max-width: 768px) {
      width: 100%;
      justify-content: flex-start;
    }

    .action-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 20px;
      border-radius: 12px;
      text-decoration: none;
      font-weight: 600;
      font-size: 14px;
      transition: all 0.2s ease;
      white-space: nowrap;

      &.primary {
        background: #25d366;
        color: white;
        box-shadow: 0 2px 8px rgba(37, 211, 102, 0.3);

        &:hover {
          background: #20ba5a;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
          text-decoration: none;
          color: white;
        }
      }

      &.secondary {
        background: #f8fafc;
        color: #475569;
        border: 1px solid #e2e8f0;

        &:hover {
          background: #f1f5f9;
          border-color: #cbd5e1;
          transform: translateY(-1px);
          text-decoration: none;
          color: #475569;
        }
      }

      i {
        font-size: 16px;
      }
    }
  }
}

// ===== INFO GRID =====
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

// ===== SHARED CARD STYLES =====
.info-card, .timeline-card, .links-card, .website-card, .notes-card, .interests-card {
  background: white;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-color: #d1d5db;
  }

  .card-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #f3f4f6;
    display: flex;
    align-items: center;
    gap: 12px;

    i {
      font-size: 18px;
      color: #2563eb;
    }

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .card-content {
    padding: 20px 24px;
  }
}

// ===== INFO ITEMS =====
.info-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 0;

  &:not(:last-child) {
    border-bottom: 1px solid #f9fafb;
  }

  .info-icon {
    width: 40px;
    height: 40px;
    background: #f8fafc;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    &.instagram {
      background: #f3f4f6;
      color: #e1306c;
    }

    i {
      font-size: 18px;
      color: #6b7280;
    }
  }

  .info-details {
    flex: 1;
    min-width: 0;

    .info-label {
      display: block;
      font-size: 12px;
      font-weight: 500;
      color: #9ca3af;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 4px;
    }

    .info-value {
      display: block;
      font-size: 15px;
      font-weight: 500;
      color: #374151;
      word-break: break-all;

      &.link {
        color: #6366f1;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

// ===== TIMELINE =====
.timeline-content {
  padding: 20px 24px;

  .timeline-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px 0;

    &:not(:last-child) {
      border-bottom: 1px solid #f9fafb;
    }

    &.priority {
      background: #fef7ee;
      margin: 0 -24px;
      padding: 16px 24px;
      border-radius: 12px;
    }

    .timeline-icon {
      width: 40px;
      height: 40px;
      background: #f0f9ff;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      i {
        font-size: 16px;
        color: #0ea5e9;
      }
    }

    .timeline-details {
      flex: 1;

      .timeline-label {
        display: block;
        font-size: 12px;
        font-weight: 500;
        color: #9ca3af;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 4px;
      }

      .timeline-value {
        display: block;
        font-size: 15px;
        font-weight: 500;
        color: #374151;

        &.highlight {
          color: #ea580c;
          font-weight: 600;
        }
      }
    }
  }
}

// ===== LINKS =====
.links-grid {
  padding: 20px 24px;
  display: flex;
  flex-direction: column;
  gap: 12px;

  .link-item {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: #f8fafc;
    border-radius: 12px;
    text-decoration: none;
    transition: all 0.2s ease;

    &:hover {
      background: #f1f5f9;
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    .link-icon {
      width: 40px;
      height: 40px;
      background: white;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      i {
        font-size: 18px;
      }
    }

    .link-content {
      flex: 1;
      min-width: 0;

      .link-type {
        display: block;
        font-size: 14px;
        font-weight: 600;
        color: #374151;
        margin-bottom: 2px;
      }

      .link-description {
        display: block;
        font-size: 13px;
        color: #6b7280;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .link-action {
      color: #9ca3af;
      font-size: 14px;
    }
  }
}

// ===== WEBSITE CARD =====
.website-card {
  .website-link {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px 24px;
    text-decoration: none;
    transition: all 0.2s ease;

    &:hover {
      background: #f8fafc;
    }

    .website-icon {
      width: 48px;
      height: 48px;
      background: #2563eb;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;

      i {
        font-size: 20px;
      }
    }

    .website-content {
      flex: 1;
      min-width: 0;

      .website-label {
        display: block;
        font-size: 12px;
        font-weight: 500;
        color: #9ca3af;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 4px;
      }

      .website-url {
        display: block;
        font-size: 15px;
        font-weight: 500;
        color: #6366f1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .website-action {
      color: #9ca3af;
      font-size: 16px;
    }
  }
}

// ===== NOTES =====
.notes-content {
  padding: 20px 24px;

  .note-section {
    &:not(:last-child) {
      margin-bottom: 24px;
      padding-bottom: 24px;
      border-bottom: 1px solid #f3f4f6;
    }

    .note-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;

      i {
        font-size: 16px;
        color: #6366f1;
      }

      span {
        font-size: 14px;
        font-weight: 600;
        color: #374151;
      }
    }

    .note-text {
      margin: 0;
      font-size: 14px;
      line-height: 1.6;
      color: #4b5563;
      background: #f8fafc;
      padding: 16px;
      border-radius: 8px;
      border-left: 3px solid #e5e7eb;
    }
  }
}

// ===== INTERESTS =====
.interests-content {
  padding: 20px 24px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .interest-tag {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: #f0f9ff;
    color: #0369a1;
    border-radius: 16px;
    font-size: 13px;
    font-weight: 500;

    i {
      font-size: 10px;
    }
  }
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 768px) {
  .lead-detail-layout {
    padding: 20px 16px;
    gap: 20px;
  }

  .modern-header {
    padding: 16px 20px;

    .header-brand {
      .brand-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
      }

      .brand-text {
        .brand-title {
          font-size: 20px;
        }

        .brand-subtitle {
          font-size: 13px;
        }
      }
    }
  }

  .hero-card {
    padding: 24px 20px;

    .company-info .company-name {
      font-size: 24px;
    }

    .hero-actions {
      .action-btn {
        padding: 10px 16px;
        font-size: 13px;

        span {
          display: none;
        }
      }
    }
  }

  .info-card, .timeline-card, .links-card, .website-card, .notes-card, .interests-card {
    .card-header {
      padding: 16px 20px 12px;

      i {
        font-size: 16px;
      }

      h3 {
        font-size: 16px;
      }
    }

    .card-content {
      padding: 16px 20px;
    }
  }
}