<div class="crm-home-container">
  <!-- Modern Header -->
  <div class="modern-header">
    <div class="header-brand">
      <div class="brand-icon">
        <i class="fa fa-users"></i>
      </div>
      <div class="brand-text">
        <h1 class="brand-title">Assistente de Vendas</h1>
        <span class="brand-subtitle">CardápioTech CRM</span>
      </div>
    </div>
    <div class="header-actions">
      <div class="status-indicator">
        <span class="status-dot active"></span>
        <span class="status-text">Online</span>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="carregandoLead">
    <div class="loading-card">
      <div class="loading-animation">
        <div class="spinner-modern"></div>
      </div>
      <h3 class="loading-title">Carregando dados do lead</h3>
      <p class="loading-text">Buscando informações para @{{ username }}...</p>
    </div>
  </div>

  <!-- Lead Not Found -->
  <div class="loading-container" *ngIf="!carregandoLead && !leadEncontrado && username">
    <div class="loading-card warning">
      <div class="warning-icon">
        <i class="fa fa-exclamation-triangle"></i>
      </div>
      <h3 class="loading-title">Lead não encontrado</h3>
      <p class="loading-text">Não foi possível encontrar dados para @{{ username }}. Redirecionando...</p>
    </div>
  </div>

  <!-- Modern Lead Detail Layout -->
  <div class="lead-detail-layout" *ngIf="!carregandoLead && leadEncontrado && dadosLeadAtual">

    <!-- Hero Header Card -->
    <div class="hero-card">
      <div class="hero-content">
        <!-- Company & Contact Info -->
        <div class="hero-main">
          <div class="company-info">
            <h1 class="company-name">{{ dadosLeadAtual?.empresa || 'Empresa não informada' }}</h1>
            <div class="contact-person">
              <i class="fa fa-user"></i>
              <span>{{ dadosLeadAtual?.nome || 'Contato não informado' }}</span>
            </div>
          </div>
          
          <!-- Score & Stage Badges -->
          <div class="status-badges">
            <div class="score-badge" [ngStyle]="{'background-color': getCorDoScore(dadosLeadAtual?.scoreLead)}">
              <i class="fa fa-star"></i>
              <span>{{ formatarScore(dadosLeadAtual?.scoreLead) }}</span>
            </div>
            <div class="stage-badge" [ngClass]="'stage-' + (dadosLeadAtual?.etapaFunil?.toLowerCase() || 'indefinida')">
              <i class="fa fa-flag"></i>
              <span>{{ dadosLeadAtual?.etapaFunil || 'Indefinida' }}</span>
            </div>
          </div>
        </div>

        <!-- Primary Action Buttons -->
        <div class="hero-actions">
          <a class="action-btn primary" 
             *ngIf="dadosLeadAtual?.telefone"
             [href]="getWhatsAppUrl(dadosLeadAtual?.telefone)"
             target="_blank"
             title="Enviar WhatsApp">
            <i class="fab fa-whatsapp"></i>
            <span>WhatsApp</span>
          </a>
          <a class="action-btn secondary" 
             *ngIf="dadosLeadAtual?.telefone"
             [href]="'tel:' + dadosLeadAtual?.telefone"
             title="Ligar agora">
            <i class="fa fa-phone"></i>
            <span>Ligar</span>
          </a>
          <a class="action-btn secondary" 
             *ngIf="dadosLeadAtual?.email"
             [href]="'mailto:' + dadosLeadAtual?.email"
             title="Enviar email">
            <i class="fa fa-envelope"></i>
            <span>Email</span>
          </a>
        </div>
      </div>
    </div>

    <!-- Quick Info Grid -->
    <div class="info-grid">
      <!-- Contact Information -->
      <div class="info-card">
        <div class="card-header">
          <i class="fa fa-address-book"></i>
          <h3>Contato</h3>
        </div>
        <div class="card-content">
          <div class="info-item" *ngIf="dadosLeadAtual?.telefone">
            <div class="info-icon">
              <i class="fa fa-phone"></i>
            </div>
            <div class="info-details">
              <span class="info-label">Telefone</span>
              <span class="info-value">{{ dadosLeadAtual?.telefone }}</span>
            </div>
          </div>
          
          <div class="info-item" *ngIf="dadosLeadAtual?.email">
            <div class="info-icon">
              <i class="fa fa-envelope"></i>
            </div>
            <div class="info-details">
              <span class="info-label">Email</span>
              <span class="info-value">{{ dadosLeadAtual?.email }}</span>
            </div>
          </div>

          <div class="info-item" *ngIf="dadosLeadAtual?.instagram">
            <div class="info-icon instagram">
              <i class="fab fa-instagram"></i>
            </div>
            <div class="info-details">
              <span class="info-label">Instagram</span>
              <a [href]="getInstagramUrl(dadosLeadAtual?.instagram)" 
                 target="_blank" 
                 class="info-value link">{{ dadosLeadAtual?.instagram }}</a>
            </div>
          </div>
        </div>
      </div>

      <!-- Business Information -->
      <div class="info-card">
        <div class="card-header">
          <i class="fa fa-building"></i>
          <h3>Negócio</h3>
        </div>
        <div class="card-content">
          <div class="info-item" *ngIf="dadosLeadAtual?.segmento">
            <div class="info-icon">
              <i class="fa fa-tag"></i>
            </div>
            <div class="info-details">
              <span class="info-label">Segmento</span>
              <span class="info-value">{{ dadosLeadAtual?.segmento }}</span>
            </div>
          </div>

          <div class="info-item" *ngIf="dadosLeadAtual?.origemLead">
            <div class="info-icon">
              <i class="fa fa-external-link"></i>
            </div>
            <div class="info-details">
              <span class="info-label">Origem</span>
              <span class="info-value">{{ dadosLeadAtual?.origemLead }}</span>
            </div>
          </div>

          <div class="info-item" *ngIf="dadosLeadAtual?.tamanhoEmpresa">
            <div class="info-icon">
              <i class="fa fa-users"></i>
            </div>
            <div class="info-details">
              <span class="info-label">Porte</span>
              <span class="info-value">{{ dadosLeadAtual?.tamanhoEmpresa }}</span>
            </div>
          </div>

          <div class="info-item" *ngIf="dadosLeadAtual?.localizacao">
            <div class="info-icon">
              <i class="fa fa-map-marker"></i>
            </div>
            <div class="info-details">
              <span class="info-label">Localização</span>
              <span class="info-value">{{ dadosLeadAtual?.localizacao }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Timeline Information -->
    <div class="timeline-card" *ngIf="dadosLeadAtual?.dataPrimeiroContato || dadosLeadAtual?.ultimaInteracao || dadosLeadAtual?.proximoFollowUp">
      <div class="card-header">
        <i class="fa fa-clock-o"></i>
        <h3>Timeline</h3>
      </div>
      <div class="timeline-content">
        <div class="timeline-item" *ngIf="dadosLeadAtual?.dataPrimeiroContato">
          <div class="timeline-icon">
            <i class="fa fa-handshake-o"></i>
          </div>
          <div class="timeline-details">
            <span class="timeline-label">Primeiro Contato</span>
            <span class="timeline-value">{{ dadosLeadAtual?.dataPrimeiroContato }}</span>
          </div>
        </div>

        <div class="timeline-item" *ngIf="dadosLeadAtual?.ultimaInteracao">
          <div class="timeline-icon">
            <i class="fa fa-comments"></i>
          </div>
          <div class="timeline-details">
            <span class="timeline-label">Última Interação</span>
            <span class="timeline-value">{{ dadosLeadAtual?.ultimaInteracao }}</span>
          </div>
        </div>

        <div class="timeline-item priority" *ngIf="dadosLeadAtual?.proximoFollowUp">
          <div class="timeline-icon">
            <i class="fa fa-calendar"></i>
          </div>
          <div class="timeline-details">
            <span class="timeline-label">Próximo Follow-up</span>
            <span class="timeline-value highlight">{{ dadosLeadAtual?.proximoFollowUp }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Business Links -->
    <div class="links-card" *ngIf="dadosLeadAtual?.links && dadosLeadAtual?.links.length > 0">
      <div class="card-header">
        <i class="fa fa-link"></i>
        <h3>Links Importantes</h3>
      </div>
      <div class="links-grid">
        <a *ngFor="let link of dadosLeadAtual?.links" 
           [href]="getLinkUrl(link)"
           target="_blank"
           class="link-item"
           [title]="link.descricao || 'Abrir ' + link.tipo">
          <div class="link-icon" [style.color]="getLinkColor(link.tipo)">
            <i [ngClass]="getLinkIcon(link.tipo)"></i>
          </div>
          <div class="link-content">
            <span class="link-type">{{ link.tipo }}</span>
            <span class="link-description">{{ link.descricao || link.url }}</span>
          </div>
          <div class="link-action">
            <i class="fa fa-external-link"></i>
          </div>
        </a>
      </div>
    </div>

    <!-- Website Link -->
    <div class="website-card" *ngIf="dadosLeadAtual?.site">
      <a [href]="getWebsiteUrl(dadosLeadAtual?.site)" 
         target="_blank"
         class="website-link">
        <div class="website-icon">
          <i class="fa fa-globe"></i>
        </div>
        <div class="website-content">
          <span class="website-label">Website</span>
          <span class="website-url">{{ dadosLeadAtual?.site }}</span>
        </div>
        <div class="website-action">
          <i class="fa fa-external-link"></i>
        </div>
      </a>
    </div>

    <!-- Notes & Observations -->
    <div class="notes-card" *ngIf="dadosLeadAtual?.observacoes || dadosLeadAtual?.historicoPropostas">
      <div class="card-header">
        <i class="fa fa-sticky-note"></i>
        <h3>Observações</h3>
      </div>
      <div class="notes-content">
        <div class="note-section" *ngIf="dadosLeadAtual?.observacoes">
          <div class="note-header">
            <i class="fa fa-edit"></i>
            <span>Observações de Vendas</span>
          </div>
          <p class="note-text">{{ dadosLeadAtual?.observacoes }}</p>
        </div>

        <div class="note-section" *ngIf="dadosLeadAtual?.historicoPropostas">
          <div class="note-header">
            <i class="fa fa-file-text"></i>
            <span>Histórico de Propostas</span>
          </div>
          <p class="note-text">{{ dadosLeadAtual?.historicoPropostas }}</p>
        </div>
      </div>
    </div>

    <!-- Interests Tags -->
    <div class="interests-card" *ngIf="dadosLeadAtual?.interessesProdutos && dadosLeadAtual?.interessesProdutos.length > 0">
      <div class="card-header">
        <i class="fa fa-star"></i>
        <h3>Interesses</h3>
      </div>
      <div class="interests-content">
        <span class="interest-tag" *ngFor="let interesse of dadosLeadAtual?.interessesProdutos">
          <i class="fa fa-tag"></i>
          {{ interesse }}
        </span>
      </div>
    </div>

  </div>
</div>