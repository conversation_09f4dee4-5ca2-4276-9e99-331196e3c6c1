<div class="container-fluid">
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h3><i class="fa fa-users"></i> Gerenciamento de Leads</h3>
    <div class="d-flex">
      <button class="btn btn-outline-secondary mr-2" (click)="toggleFiltros()">
        <i class="fa fa-filter"></i> Filtros
      </button>
      <button class="btn btn-primary" (click)="novo()" *ngIf="!modoEdicao">
        <i class="fa fa-plus"></i> Novo Lead
      </button>
    </div>
  </div>

  <!-- Filtros Rápidos -->
  <div class="card mb-4" *ngIf="mostrarFiltros">
    <div class="card-body">
      <div class="row">
        <div class="col-md-3">
          <label><strong>Etapa</strong></label>
          <select class="form-control" [(ngModel)]="filtroEtapa" (ngModelChange)="aplicarFiltros()">
            <option value="">Todas as Etapas</option>
            <option *ngFor="let etapa of etapas" [value]="etapa.valor">{{ etapa.texto }}</option>
          </select>
        </div>
        <div class="col-md-3">
          <label><strong>Origem</strong></label>
          <select class="form-control" [(ngModel)]="filtroOrigem" (ngModelChange)="aplicarFiltros()">
            <option value="">Todas as Origens</option>
            <option *ngFor="let origem of origens" [value]="origem.valor">{{ origem.texto }}</option>
          </select>
        </div>
        <div class="col-md-4">
          <label><strong>Buscar</strong></label>
          <input type="text" class="form-control" [(ngModel)]="filtroTexto" 
                 (ngModelChange)="aplicarFiltros()" placeholder="Nome, empresa ou telefone...">
        </div>
        <div class="col-md-2">
          <label><strong>Pendências</strong></label>
          <div class="form-check">
            <input type="checkbox" class="form-check-input" [(ngModel)]="filtroPendencias" 
                   (ngModelChange)="aplicarFiltros()">
            <label class="form-check-label">Só pendentes</label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Formulário de Cadastro/Edição -->
  <div class="card mb-4" *ngIf="modoEdicao">
    <div class="card-header">
      <h5 class="mb-0">
        <i class="fa fa-edit"></i> 
        {{ leadSelecionado.id ? 'Editar Lead' : 'Novo Lead' }}
      </h5>
    </div>
    <div class="card-body">
      <form #leadForm="ngForm" (ngSubmit)="salvar()">
        
        <!-- Busca Instagram -->
        <div class="card mb-4 bg-light" *ngIf="!leadSelecionado.id">
          <div class="card-header py-2">
            <h6 class="mb-0">
              <i class="fab fa-instagram text-danger mr-2"></i>
              Buscar Dados do Instagram
            </h6>
          </div>
          <div class="card-body py-3">
            <div class="row">
              <div class="col-md-8">
                <div class="form-group mb-2">
                  <label for="instagramUsername"><strong>Username do Instagram</strong></label>
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <span class="input-group-text">@</span>
                    </div>
                    <input type="text" 
                           id="instagramUsername"
                           class="form-control" 
                           [(ngModel)]="instagramUsername"
                           name="instagramUsername"
                           placeholder="usuario_instagram"
                           [disabled]="buscandoInstagram">
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <label>&nbsp;</label>
                <div class="form-group">
                  <button type="button" 
                          class="btn btn-info btn-block"
                          (click)="buscarDadosInstagram()"
                          [disabled]="buscandoInstagram || !instagramUsername">
                    <i class="fa fa-search mr-2" *ngIf="!buscandoInstagram"></i>
                    <i class="fa fa-spinner fa-spin mr-2" *ngIf="buscandoInstagram"></i>
                    {{ buscandoInstagram ? 'Buscando...' : 'Buscar Dados' }}
                  </button>
                </div>
              </div>
            </div>
            <small class="text-muted">
              <i class="fa fa-info-circle mr-1"></i>
              Digite o username (sem @) para buscar automaticamente os dados do perfil e criar/vincular a empresa.
            </small>
          </div>
        </div>

        <!-- Dados Básicos -->
        <div class="row">
          <div class="col-md-4">
            <div class="form-group">
              <label for="nomeResponsavel"><strong>Nome do Responsável *</strong></label>
              <input type="text" 
                     id="nomeResponsavel"
                     name="nomeResponsavel" 
                     class="form-control" 
                     [(ngModel)]="leadSelecionado.nomeResponsavel" 
                     required
                     placeholder="Ex: João Silva">
            </div>
          </div>
          
          <div class="col-md-4">
            <div class="form-group">
              <label for="empresa"><strong>Empresa/Estabelecimento *</strong></label>
              <input type="text" 
                     id="empresa"
                     name="empresa" 
                     class="form-control" 
                     [(ngModel)]="leadSelecionado.empresa" 
                     required
                     placeholder="Ex: Pizzaria do João">
            </div>
          </div>
          
          <div class="col-md-4">
            <div class="form-group">
              <label for="telefone"><strong>Telefone/WhatsApp *</strong></label>
              <input type="text" 
                     id="telefone"
                     name="telefone" 
                     class="form-control" 
                     [(ngModel)]="leadSelecionado.telefone" 
                     (ngModelChange)="formatarTelefone()"
                     required
                     placeholder="(11) 99999-9999">
            </div>
          </div>
        </div>

        <!-- Empresa CRM -->
        <div class="row">
          <div class="col-md-12">
            <div class="form-group">
              <label for="crmEmpresa"><strong>Empresa CRM</strong></label>
              <select id="crmEmpresa" 
                      name="crmEmpresa" 
                      class="form-control" 
                      [(ngModel)]="leadSelecionado.crmEmpresa">
                <option [ngValue]="null">Selecione uma empresa CRM...</option>
                <option *ngFor="let empresa of crmEmpresas" [ngValue]="empresa">
                  {{ empresa.nome }}
                </option>
              </select>
              <small class="text-muted">Empresa no sistema CRM que este lead será vinculado</small>
            </div>
          </div>
        </div>

        <!-- Informações de Processo -->
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="etapa"><strong>Etapa do Funil</strong></label>
              <select id="etapa" 
                      name="etapa" 
                      class="form-control" 
                      [(ngModel)]="leadSelecionado.etapa">
                <option *ngFor="let etapa of etapas" [value]="etapa.valor">
                  {{ etapa.texto }}
                </option>
              </select>
            </div>
          </div>
          
          <div class="col-md-3">
            <div class="form-group">
              <label for="origem"><strong>Origem</strong></label>
              <select id="origem" 
                      name="origem" 
                      class="form-control" 
                      [(ngModel)]="leadSelecionado.origem">
                <option *ngFor="let origem of origens" [value]="origem.valor">
                  {{ origem.texto }}
                </option>
              </select>
            </div>
          </div>
          
          <div class="col-md-3">
            <div class="form-group">
              <label for="segmento"><strong>Segmento</strong></label>
              <select id="segmento" 
                      name="segmento" 
                      class="form-control" 
                      [(ngModel)]="leadSelecionado.segmento">
                <option value="">Selecione...</option>
                <option *ngFor="let segmento of segmentos" [value]="segmento.valor">
                  {{ segmento.texto }}
                </option>
              </select>
            </div>
          </div>
          
          <div class="col-md-3">
            <div class="form-group">
              <label for="score"><strong>Score (0-100)</strong></label>
              <input type="number" 
                     id="score"
                     name="score" 
                     class="form-control" 
                     [(ngModel)]="leadSelecionado.score" 
                     min="0" 
                     max="100"
                     placeholder="0">
            </div>
          </div>
        </div>

        <!-- Instagram e Link -->
        <div class="row">
          <div class="col-md-4">
            <div class="form-group">
              <label for="instagram"><strong>Instagram</strong></label>
              <div class="input-group">
                <div class="input-group-prepend">
                  <span class="input-group-text">@</span>
                </div>
                <input type="text" 
                       id="instagram"
                       name="instagramHandle" 
                       class="form-control" 
                       [(ngModel)]="leadSelecionado.instagramHandle" 
                       (ngModelChange)="formatarInstagram()"
                       placeholder="usuario_instagram">
              </div>
            </div>
          </div>
          
          <div class="col-md-4">
            <div class="form-group">
              <label for="linkInsta"><strong>Link da Bio</strong></label>
              <input type="url" 
                     id="linkInsta"
                     name="linkInsta" 
                     class="form-control" 
                     [(ngModel)]="leadSelecionado.linkInsta" 
                     placeholder="https://site.com.br">
              <small class="text-muted">Link externo da biografia do Instagram</small>
            </div>
          </div>
          
          <div class="col-md-4">
            <div class="form-group">
              <label for="valorPotencial"><strong>Valor Potencial (R$)</strong></label>
              <input type="number" 
                     id="valorPotencial"
                     name="valorPotencial" 
                     class="form-control" 
                     [(ngModel)]="leadSelecionado.valorPotencial" 
                     step="0.01"
                     placeholder="0,00">
            </div>
          </div>
        </div>

        <!-- Bio Instagram -->
        <div class="row" *ngIf="leadSelecionado.bioInsta">
          <div class="col-12">
            <div class="form-group">
              <label for="bioInsta"><strong>Bio do Instagram</strong></label>
              <textarea id="bioInsta"
                        name="bioInsta" 
                        class="form-control" 
                        [(ngModel)]="leadSelecionado.bioInsta" 
                        rows="2"
                        readonly
                        placeholder="Bio extraída do Instagram..."></textarea>
              <small class="text-muted">
                <i class="fa fa-info-circle mr-1"></i>
                Bio extraída automaticamente do perfil do Instagram
              </small>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-12">
            <div class="form-group">
              <label for="notas"><strong>Observações</strong></label>
              <textarea id="notas"
                        name="notas"
                        class="form-control"
                        [(ngModel)]="leadSelecionado.notas"
                        rows="3"
                        placeholder="Observações sobre o lead..."></textarea>
            </div>
          </div>
        </div>

        <!-- Seção de Links -->
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                  <i class="fa fa-link mr-2"></i>Links do Lead
                </h6>
                <button type="button"
                        class="btn btn-sm btn-outline-primary"
                        (click)="toggleSecaoLinks()">
                  <i class="fa" [class.fa-chevron-down]="!mostrarSecaoLinks"
                                [class.fa-chevron-up]="mostrarSecaoLinks"></i>
                  {{ mostrarSecaoLinks ? 'Ocultar' : 'Mostrar' }}
                </button>
              </div>

              <div class="card-body" *ngIf="mostrarSecaoLinks">
                <!-- Links existentes -->
                <div class="mb-3" *ngIf="leadSelecionado.links && leadSelecionado.links.length > 0">
                  <h6>Links Cadastrados:</h6>
                  <div class="row">
                    <div class="col-md-6 mb-2" *ngFor="let link of leadSelecionado.links; let i = index">
                      <div class="card border-left-primary">
                        <div class="card-body p-2">
                          <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                              <i class="fa mr-2"
                                 [ngClass]="getTipoLinkInfo(link.tipo).icone"
                                 [style.color]="getTipoLinkInfo(link.tipo).cor"></i>
                              <div>
                                <strong>{{ getTipoLinkInfo(link.tipo).texto }}</strong>
                                <br>
                                <small class="text-muted">{{ link.url }}</small>
                                <div *ngIf="link.descricao" class="text-muted" style="font-size: 0.8rem;">
                                  {{ link.descricao }}
                                </div>
                              </div>
                            </div>
                            <div class="btn-group">
                              <button type="button"
                                      class="btn btn-sm btn-outline-info"
                                      (click)="abrirLink(link.url, link.tipo)"
                                      title="Abrir link">
                                <i class="fa fa-external-link-alt"></i>
                              </button>
                              <button type="button"
                                      class="btn btn-sm btn-outline-danger"
                                      (click)="removerLink(i)"
                                      title="Remover link">
                                <i class="fa fa-trash"></i>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Formulário para adicionar novo link -->
                <div class="border-top pt-3">
                  <h6>Adicionar Novo Link:</h6>
                  <div class="row">
                    <div class="col-md-3">
                      <div class="form-group">
                        <label>Tipo</label>
                        <select class="form-control form-control-sm"
                                [(ngModel)]="novoLink.tipo"
                                name="novoLinkTipo">
                          <option value="">Selecione...</option>
                          <option *ngFor="let tipo of tiposLink" [value]="tipo.valor">
                            {{ tipo.texto }}
                          </option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label>URL</label>
                        <input type="text"
                               class="form-control form-control-sm"
                               [(ngModel)]="novoLink.url"
                               name="novoLinkUrl"
                               placeholder="https://exemplo.com">
                      </div>
                    </div>
                    <div class="col-md-3">
                      <div class="form-group">
                        <label>Descrição (opcional)</label>
                        <input type="text"
                               class="form-control form-control-sm"
                               [(ngModel)]="novoLink.descricao"
                               name="novoLinkDescricao"
                               placeholder="Descrição do link">
                      </div>
                    </div>
                    <div class="col-md-2">
                      <div class="form-group">
                        <label>&nbsp;</label>
                        <button type="button"
                                class="btn btn-success btn-sm btn-block"
                                (click)="adicionarLink()"
                                [disabled]="!novoLink.tipo || !novoLink.url">
                          <i class="fa fa-plus"></i> Adicionar
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Botões -->
        <div class="d-flex justify-content-end">
          <button type="button" class="btn btn-secondary mr-2" (click)="cancelar()">
            <i class="fa fa-times"></i> Cancelar
          </button>
          <button type="submit" 
                  class="btn btn-success" 
                  [disabled]="leadForm.invalid">
            <i class="fa fa-save"></i> Salvar
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Loading -->
  <div class="text-center py-4" *ngIf="carregando">
    <i class="fa fa-spinner fa-spin fa-2x"></i>
    <p class="mt-2">Carregando leads...</p>
  </div>

  <!-- Lista de Cards dos Leads -->
  <div class="row" *ngIf="!modoEdicao && !carregando">
    <div class="col-12 mb-3" *ngIf="leadsFiltrados.length === 0">
      <div class="card">
        <div class="card-body text-center py-5">
          <i class="fa fa-users fa-3x text-muted mb-3"></i>
          <h5 class="text-muted">Nenhum lead encontrado</h5>
          <p class="text-muted">Adicione um novo lead ou ajuste os filtros</p>
        </div>
      </div>
    </div>

    <div class="col-lg-6 col-xl-4 mb-3" *ngFor="let lead of leadsFiltrados">
      <div class="card lead-card h-100 shadow-sm" 
           [class.border-warning]="isAtrasado(lead)"
           [class.border-success]="lead.etapa === 'Ganho'"
           [class.border-danger]="lead.etapa === 'Perdido'"
           [class.border-info]="lead.etapa === 'Fechamento'"
           [class.border-primary]="lead.etapa === 'Qualificação'">
        
        <!-- Header Compacto -->
                 <div class="card-header py-2 px-3"
              [class.bg-gradient-success]="lead.etapa === 'Ganho'"
              [class.bg-gradient-danger]="lead.etapa === 'Perdido'"
              [class.bg-gradient-warning]="isAtrasado(lead)"
              [class.bg-gradient-info]="lead.etapa === 'Fechamento'"
              [class.bg-gradient-primary]="lead.etapa === 'Qualificação'"
              [class.bg-gradient-secondary]="lead.etapa === 'Prospecção'">
          
          <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <i class="fa {{ getIconeEtapa(lead.etapa) }} mr-2"></i>
                             <span class="font-weight-light" style="font-size: 0.85rem;">{{ lead.etapa }}</span>
            </div>
            
            <div class="d-flex align-items-center">
              <span class="badge badge-light badge-pill mr-2 px-2"
                    [style.background-color]="getCorScore(lead.score)"
                    [style.color]="'white'"
                    style="font-size: 0.7rem;">
                {{ lead.score || 0 }}%
              </span>
              
              <div class="dropdown">
                                 <button class="btn btn-sm btn-link p-0" data-toggle="dropdown">
                  <i class="fa fa-ellipsis-v"></i>
                </button>
                <div class="dropdown-menu dropdown-menu-right">
                  <a class="dropdown-item" href="#" (click)="editar(lead)">
                    <i class="fa fa-edit mr-2 text-primary"></i> Editar
                  </a>
                  <a class="dropdown-item text-danger" href="#" (click)="remover(lead.id)">
                    <i class="fa fa-trash mr-2"></i> Remover
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Corpo Compacto -->
        <div class="card-body p-3">
          <!-- Nome do Responsável -->
          <div class="mb-2">
            <h6 class="card-title mb-2 text-dark font-weight-light" style="font-size: 1rem;">
              <i class="fa fa-user text-primary mr-2"></i>
              {{ lead.nomeResponsavel }}
            </h6>
          </div>

          <!-- Origem e Segmento em linha -->
          <div class="d-flex flex-wrap mb-2">
            <span class="badge mr-1 mb-1 px-2 py-1" 
                  [class.badge-instagram]="lead.origem === 'Instagram'"
                  [class.badge-whatsapp]="lead.origem === 'WhatsApp Direto'"
                  [class.badge-site]="lead.origem === 'Site/Landing Page'"
                  [class.badge-indicacao]="lead.origem === 'Indicação'"
                  [class.badge-secondary]="!['Instagram', 'WhatsApp Direto', 'Site/Landing Page', 'Indicação'].includes(lead.origem)"
                  style="font-size: 0.7rem;">
              {{ lead.origem }}
            </span>
            <span class="badge badge-outline-info mb-1 px-2 py-1" 
                  *ngIf="lead.segmento"
                  style="font-size: 0.7rem;">
              {{ lead.segmento }}
            </span>
          </div>

          <!-- Contatos em linha compacta -->
          <div class="contact-info mb-2">
            <div class="d-flex align-items-center justify-content-between mb-1" *ngIf="lead.telefone">
              <div class="d-flex align-items-center">
                <i class="fa fa-phone text-success mr-2" style="font-size: 0.8rem;"></i>
                <span style="font-size: 0.8rem;">{{ lead.telefone }}</span>
              </div>
              <div class="d-flex">
                <button class="btn btn-open-lead btn-xs mr-1" 
                        (click)="abrirLead(lead)"
                        title="Abrir Lead">
                  <i class="fa fa-external-link-alt mr-1"></i>
                  <span>Lead</span>
                </button>
                <a [href]="getWhatsappUrl(lead.telefone)" 
                   target="_blank" class="btn btn-whatsapp btn-xs">
                  <i class="fa fa-whatsapp"></i>
                </a>
              </div>
            </div>
            
            <div class="d-flex align-items-center justify-content-between mb-1" *ngIf="lead.instagramHandle">
              <div class="d-flex align-items-center">
                <i class="fab fa-instagram text-danger mr-2" style="font-size: 0.8rem;"></i>
                <span style="font-size: 0.8rem;">@{{ lead.instagramHandle }}</span>
              </div>
              <div class="d-flex gap-1">
                <a [href]="getInstagramUrl(lead.instagramHandle)" 
                   target="_blank" class="btn btn-instagram btn-xs" title="Ver perfil">
                  <i class="fab fa-instagram"></i>
                </a>
                <a *ngIf="lead.linkInsta" 
                   [href]="lead.linkInsta" 
                   target="_blank" class="btn btn-link btn-xs" title="Link da bio">
                  <i class="fa fa-external-link-alt"></i>
                </a>
              </div>
            </div>
          </div>

          <!-- Valor Potencial -->
          <div class="mb-2" *ngIf="lead.valorPotencial">
            <i class="fa fa-dollar-sign text-success mr-1"></i>
            <strong class="text-success" style="font-size: 0.8rem;">{{ formatarValor(lead.valorPotencial) }}</strong>
          </div>

          <!-- CRM Empresa como tag -->
          <div class="mb-2">
            <span class="badge badge-crm-empresa px-2 py-1" style="font-size: 0.7rem;" *ngIf="lead.empresa">
              <i class="fa fa-building mr-1"></i>
              {{ lead.empresa }}
            </span>
          </div>

          <!-- Links do Lead -->
          <div class="mb-2" *ngIf="lead.links && lead.links.length > 0">
            <div class="d-flex flex-wrap">
              <a *ngFor="let link of lead.links"
                 [href]="formatarUrlLink(link.url, link.tipo)"
                 target="_blank"
                 class="btn btn-xs mr-1 mb-1"
                 [style.background-color]="getTipoLinkInfo(link.tipo).cor"
                 [style.color]="'white'"
                 [title]="link.descricao || getTipoLinkInfo(link.tipo).texto">
                <i class="fa {{ getTipoLinkInfo(link.tipo).icone }} mr-1"></i>
                <span style="font-size: 0.7rem;">{{ getTipoLinkInfo(link.tipo).texto }}</span>
              </a>
            </div>
          </div>

          <!-- Alerta de Pendência Compacto -->
          <div class="alert alert-warning py-1 px-2 mb-2" 
               *ngIf="isAtrasado(lead)"
               style="font-size: 0.7rem;">
            <i class="fa fa-exclamation-triangle mr-1"></i>
            <strong>Follow-up atrasado!</strong>
          </div>

          <!-- Data compacta -->
          <div class="text-muted text-right" style="font-size: 0.7rem;">
            <i class="fa fa-calendar mr-1"></i>
            {{ formatarData(lead.dataCriacao) }}
          </div>
        </div>

        <!-- Footer compacto -->
        <div class="card-footer p-2 bg-light">
          <div class="row no-gutters">
            <div class="col-6 pr-1">
              <button class="btn btn-outline-primary btn-sm btn-block py-1" 
                      (click)="editar(lead)"
                      style="font-size: 0.75rem;">
                <i class="fa fa-edit mr-1"></i> Editar
              </button>
            </div>
            <div class="col-6 pl-1">
              <button class="btn btn-outline-info btn-sm btn-block py-1"
                      (click)="abrirDetalhesLead(lead)"
                      style="font-size: 0.75rem;">
                <i class="fa fa-info-circle mr-1"></i> Detalhes
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div> 