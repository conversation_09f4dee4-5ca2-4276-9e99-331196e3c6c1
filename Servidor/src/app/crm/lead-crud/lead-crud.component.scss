/* Estilo principal do container */
.container-fluid {
  padding: 1.5rem;
}

/* Cards com visual moderno */
.card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  border-radius: 8px 8px 0 0 !important;
  
  h5 {
    color: #495057;
    font-weight: 600;
  }
}

/* Formulário responsivo */
.form-group {
  margin-bottom: 1.25rem;
  
  label {
    color: #495057;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }
  
  .form-control {
    border-radius: 4px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    
    &:focus {
      border-color: #007bff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    &.is-invalid {
      border-color: #dc3545;
    }
  }
  
  select.form-control {
    height: 38px;
  }
}

/* Input Group personalizado */
.input-group-text {
  background-color: #e9ecef;
  border-color: #ced4da;
  font-weight: 500;
}

/* Botões com espaçamento */
.btn {
  font-weight: 500;
  border-radius: 4px;
  
  &.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
  }
  
  &.btn-success {
    background-color: #28a745;
    border-color: #28a745;
  }
  
  &.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
  }
}

/* Grid melhorado */
.k-grid {
  border-radius: 4px;
  border: 1px solid #dee2e6;
  
  .k-grid-header {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    
    .k-header {
      font-weight: 600;
      color: #495057;
      font-size: 0.9rem;
    }
  }
  
  .k-grid-content {
    .k-table tbody tr {
      &:hover {
        background-color: #f8f9fa;
      }
    }
  }
}

/* Badges customizados */
.badge {
  font-size: 0.8rem;
  padding: 0.375rem 0.5rem;
  border-radius: 4px;
  
  &.badge-primary {
    background-color: #007bff;
  }
  
  i {
    margin-right: 0.25rem;
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .container-fluid {
    padding: 1rem;
  }
  
  .d-flex.justify-content-between {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 1rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .btn-group {
    width: 100%;
    
    .btn {
      flex: 1;
    }
  }
}

/* Loading state */
.k-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
}

/* Animações suaves */
.card, .btn, .form-control {
  transition: all 0.2s ease-in-out;
}

/* Headers com ícones */
h3, h5 {
  i {
    margin-right: 0.5rem;
    color: #007bff;
  }
}

/* Estilos para os cards de leads */
.lead-card {
  transition: all 0.3s ease;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  }
  
  /* Bordas coloridas baseadas no status */
  &.border-success {
    border-left: 4px solid #28a745;
  }
  
  &.border-danger {
    border-left: 4px solid #dc3545;
  }
  
  &.border-warning {
    border-left: 4px solid #ffc107;
  }
  
  .card-header {
    border-radius: 8px 8px 0 0;
    font-weight: 300;
    color: white;
    
    // Garantir que todos os elementos tenham cor branca
    span, i, button, .badge {
      color: white !important;
    }
    
    .btn-link {
      color: white !important;
      
      &:hover {
        color: rgba(255, 255, 255, 0.8) !important;
      }
    }
    
    &.bg-success {
      background: linear-gradient(135deg, #28a745, #20c997) !important;
    }
    
    &.bg-danger {
      background: linear-gradient(135deg, #dc3545, #e83e8c) !important;
    }
    
    &.bg-warning {
      background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
    }
  }
  
  .card-title {
    color: #2c3e50;
    font-weight: 300;
    margin-bottom: 0.25rem;
  }
  
  .card-subtitle {
    font-size: 0.95rem;
    font-weight: 500;
  }
  
  .badge {
    font-size: 0.75rem;
    font-weight: 500;
    
    &.badge-outline-primary {
      color: #007bff;
      border: 1px solid #007bff;
      background: transparent;
    }
  }
  
  .card-footer {
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa !important;
    border-radius: 0 0 8px 8px;
  }
}

/* Botões de ação */
.btn-sm {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  
  &.btn-outline-success:hover {
    background-color: #28a745;
    border-color: #28a745;
  }
  
  &.btn-outline-primary:hover {
    background-color: #007bff;
    border-color: #007bff;
  }
}

/* Ícones de contato */
.fa-phone {
  color: #28a745;
}

.fa-instagram {
  color: #e1306c;
}

.fa-whatsapp {
  color: #25d366;
}

/* Alertas */
.alert-warning {
  background-color: #fff3cd;
  border-color: #ffeaa7;
  color: #856404;
  border-radius: 6px;
}

/* Dropdown menu */
.dropdown-menu {
  border-radius: 6px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  border: none;
}

.dropdown-item {
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  
  &:hover {
    background-color: #f8f9fa;
  }
  
  &.text-danger:hover {
    background-color: #f5c6cb;
    color: #721c24;
  }
}

/* Estados de loading */
.fa-spinner {
  color: #6c757d;
}

/* Estado vazio */
.text-muted {
  color: #6c757d !important;
}

/* Responsividade */
@media (max-width: 768px) {
  .lead-card {
    margin-bottom: 1rem;
  }
  
  .card-header {
    font-size: 0.9rem;
  }
  
  .card-title {
    font-size: 1.1rem;
  }
  
  .btn-sm {
    font-size: 0.75rem;
    padding: 0.2rem 0.4rem;
  }
}

/* Animações */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.lead-card {
  animation: fadeIn 0.3s ease-out;
}

/* Melhorias nos badges */
.badge-pill {
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 600;
  border-radius: 10rem;
}

/* Hover effects nos links */
a[href*="wa.me"] {
  text-decoration: none;
  
  &:hover {
    transform: scale(1.05);
  }
}

a[href*="instagram.com"] {
  text-decoration: none;
  
  &:hover {
    transform: scale(1.05);
  }
}

/* Badges coloridos para origem */
.badge-instagram {
  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888) !important;
  color: white;
}

.badge-whatsapp {
  background: linear-gradient(45deg, #25d366, #128c7e) !important;
  color: white;
}

.badge-site {
  background: linear-gradient(45deg, #007bff, #0056b3) !important;
  color: white;
}

.badge-indicacao {
  background: linear-gradient(45deg, #6f42c1, #563d7c) !important;
  color: white;
}

.badge-outline-info {
  color: #17a2b8;
  border: 1px solid #17a2b8;
  background: rgba(23, 162, 184, 0.1);
}

/* Botões especiais compactos */
.btn-xs {
  padding: 0.15rem 0.3rem;
  font-size: 0.7rem;
  border-radius: 4px;
  transition: all 0.2s ease;
  
  &:hover {
    transform: scale(1.1);
  }
}

.btn-whatsapp {
  background: linear-gradient(45deg, #25d366, #128c7e);
  color: white;
  border: none;
  
  &:hover {
    background: linear-gradient(45deg, #128c7e, #075e54);
    color: white;
  }
}

.btn-instagram {
  background: linear-gradient(45deg, #e1306c, #c13584);
  color: white;
  border: none;
  
  &:hover {
    background: linear-gradient(45deg, #c13584, #833ab4);
    color: white;
  }
}

/* Contact info compacta */
.contact-info {
  background: rgba(0,0,0,0.02);
  border-radius: 6px;
  padding: 0.5rem;
  border: 1px solid rgba(0,0,0,0.05);
}

/* Headers com gradientes para etapas */
.card-header {
  &.bg-gradient-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
  }
  
  &.bg-gradient-danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c) !important;
  }
  
  &.bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
  }
  
  &.bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1) !important;
  }
  
  &.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff, #6610f2) !important;
  }
  
  &.bg-gradient-secondary {
    background: linear-gradient(135deg, #6c757d, #495057) !important;
  }
}

/* Melhorar bordas coloridas */
.lead-card {
  &.border-info {
    border-left: 5px solid #17a2b8;
    border-color: #17a2b8;
  }
  
  &.border-primary {
    border-left: 5px solid #007bff;
    border-color: #007bff;
  }
  
  &.border-success {
    border-left: 5px solid #28a745;
    border-color: #28a745;
  }
  
  &.border-danger {
    border-left: 5px solid #dc3545;
    border-color: #dc3545;
  }
  
  &.border-warning {
    border-left: 5px solid #ffc107;
    border-color: #ffc107;
  }
}

/* Ícones coloridos extras */
.fa-dollar-sign {
  color: #ffc107 !important;
}

.fa-calendar {
  color: #6c757d !important;
}

/* Alertas compactos melhorados */
.alert-warning {
  background: linear-gradient(135deg, #fff3cd, #ffeaa7) !important;
  border: 1px solid #ffeaa7 !important;
  color: #856404 !important;
  border-radius: 6px !important;
  font-weight: 600;
}

/* Hover effects nos badges */
.badge {
  transition: all 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

.btn-open-lead {
  background: linear-gradient(45deg, #007bff, #0056b3);
  color: white;
  border: none;
  font-size: 0.65rem;
  
  &:hover {
    background: linear-gradient(45deg, #0056b3, #003d82);
    color: white;
    transform: scale(1.05);
  }
  
  span {
    font-weight: 600;
  }
}

/* Badge da empresa */
.badge-empresa {
  background: linear-gradient(45deg, #6c757d, #495057);
  color: white;
  border-radius: 6px;
  font-weight: 500;
  
  .fa-building {
    opacity: 0.8;
  }
}

/* Badge da CRM empresa */
.badge-crm-empresa {
  background: linear-gradient(45deg, #007bff, #0056b3);
  color: white;
  border-radius: 6px;
  font-weight: 500;
  
  .fa-users {
    opacity: 0.9;
  }
}

/* Ícone de pessoa no título */
.card-title .fa-user {
  color: #007bff;
  font-size: 0.9rem;
  opacity: 0.8;
} 