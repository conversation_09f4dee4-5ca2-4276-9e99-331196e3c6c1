// Cores e variáveis
$primary: #3b7ddd; // Azul principal
$primary-dark: #2d6ec0; // Azul escuro
$primary-light: #e8f2ff; // Azul clarinho
$accent: #2ecc71; // Verde para destaque
$accent-light: #e8f8ed; // Verde clarinho
$neutral: #4a6583; // Azul acinzentado
$neutral-light: #edf1f7; // Azul acinzentado claro
$gray-light: #f8f9fa;
$gray-lighter: #f2f3f5;
$gray-border: #e0e0e0;
$gray-dark: #495057;
$text-dark: #344767;
$text-secondary: #666;
$radius: 8px;
$shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
$transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

.assistente-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background-color: #fff;
  border-radius: $radius;
  font-family: '<PERSON><PERSON><PERSON>', Robot<PERSON>, Oxygen, Ubuntu, Cantarell, sans-serif;
  color: $text-dark;
  box-shadow: $shadow;
  letter-spacing: -0.01em;
}


// Seções
.section {
  padding: 18px 24px;
  border-bottom: 1px solid $gray-border;
  position: relative;
  background-color: white;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:nth-child(even) {
    background-color: $gray-lighter;
  }
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
  flex-wrap: wrap;

  h2 {
    margin: 0;
    font-size: 16px;
    font-weight: 700;
    color: $text-dark;
    letter-spacing: -0.01em;
  }

  .icon-pin, .icon-tone, .icon-chat {
    font-size: 20px;
    color: $primary;
  }
  
  .highlighted-text {
    color: $primary;
    font-weight: 700;
    background-color: rgba(59, 125, 221, 0.1);
    padding: 2px 8px;
    border-radius: 4px;
  }
  
  .contact-info {
    margin-left: auto;
    font-size: 14px;
    color: $text-secondary;
    background-color: $gray-lighter;
    padding: 4px 10px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    gap: 6px;
    font-weight: 500;
    
    i {
      color: $primary;
    }
  }
}

// Botões de alternancia
.toggle-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.toggle-btn {
  padding: 9px 18px;
  border-radius: 22px;
  border: 1px solid $gray-border;
  background-color: white;
  font-size: 14px;
  font-weight: 600;
  color: $text-secondary;
  cursor: pointer;
  transition: $transition;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  position: relative;
  overflow: hidden;

  .btn-content {
    position: relative;
    z-index: 2;
  }
  
  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: $primary;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    z-index: 1;
    opacity: 0.1;
  }

  &:hover {
    background-color: $primary-light;
    border-color: rgba(59, 125, 221, 0.3);
    color: $primary;
    transform: translateY(-1px);
    box-shadow: 0 3px 5px rgba(0,0,0,0.08);
    
    &:before {
      transform: translateX(0);
    }
  }

  &.active {
    background-color: $primary;
    color: white;
    border-color: $primary;
    box-shadow: 0 2px 6px rgba(59, 125, 221, 0.4);
    
    &:before {
      transform: translateX(0);
      opacity: 0;
    }
  }
}

// Seção de sugestões
.suggestions-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-bottom: none;
  background-color: white !important;
}

.suggestions-container {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 14px;
  padding: 0 0 16px 0;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: $gray-lighter;
    border-radius: 10px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(59, 125, 221, 0.2);
    border-radius: 10px;
    
    &:hover {
      background: rgba(59, 125, 221, 0.4);
    }
  }
}

.suggestion-card {
  padding: 18px 20px 18px 24px;
  border-radius: $radius;
  margin: 0 4px;
  transition: $transition;
  position: relative;
  box-shadow: 0 2px 4px rgba(0,0,0,0.06);
  border-left: 4px solid transparent;
  cursor: pointer;
  
  &:nth-child(odd) {
    background-color: $accent-light;
    border-left-color: $accent;
  }
  
  &:nth-child(even) {
    background-color: $primary-light; 
    border-left-color: $primary;
  }
  
  &:nth-child(3n) {
    background-color: $neutral-light;
    border-left-color: $neutral;
  }

  &:hover {
    transform: translateY(-2px) scale(1.01);
    box-shadow: 0 6px 12px rgba(0,0,0,0.1);
  }
  
  .card-number {
    position: absolute;
    top: 12px;
    left: -6px;
    width: 24px;
    height: 24px;
    background-color: rgba(0,0,0,0.1);
    color: rgba(0,0,0,0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: $transition;
  }
  
  &:hover .card-number {
    background-color: $primary;
    color: white;
    transform: scale(1.1);
    box-shadow: 0 2px 6px rgba(59, 125, 221, 0.3);
  }
  
  .card-actions {
    position: absolute;
    right: 12px;
    bottom: 10px;
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: $transition;
  }
  
  &:hover .card-actions {
    opacity: 1;
  }
  
  .action-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: rgba(255,255,255,0.7);
    backdrop-filter: blur(4px);
    color: $text-dark;
    cursor: pointer;
    transition: $transition;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    &.copy-btn:hover {
      background: $primary-light;
      color: $primary;
    }
    
    &.send-btn:hover {
      background: $accent-light;
      color: $accent;
    }
    
    i {
      font-size: 14px;
    }
  }

  p {
    margin: 0;
    line-height: 1.6;
    font-size: 15px;
    color: $text-dark;
    font-weight: 400;
    padding-right: 16px;
  }
}

.empty-message {
  text-align: center;
  padding: 40px 20px;
  color: $text-secondary;
  font-style: italic;
  background-color: $gray-lighter;
  border-radius: $radius;
  margin: 10px;
  box-shadow: inset 0 0 6px rgba(0,0,0,0.03);
}

// Botão de atualizar
.refresh-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 18px 0 16px;
  background-color: $gray-lighter;
  border-top: 1px solid $gray-border;
  margin-top: auto;
  gap: 10px;
  
  .message-counter {
    font-size: 12px;
    color: $text-secondary;
    background-color: rgba(255,255,255,0.7);
    padding: 3px 10px;
    border-radius: 12px;
    margin-top: 5px;
  }
}

.btn-refresh {
  padding: 10px 20px;
  border-radius: 22px;
  background-color: white;
  border: 1px solid $primary;
  color: $primary;
  font-weight: 600;
  cursor: pointer;
  transition: $transition;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 4px rgba(59, 125, 221, 0.15);

  &:hover {
    background-color: $primary;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 125, 221, 0.3);
  }

  i {
    font-size: 14px;
  }
}

// Loading spinner
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  gap: 18px;

  .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(59, 125, 221, 0.2);
    border-radius: 50%;
    border-top-color: $primary;
    animation: spin 0.8s cubic-bezier(0.5, 0.1, 0.5, 0.9) infinite;
    box-shadow: 0 2px 10px rgba(59, 125, 221, 0.2);
  }

  p {
    color: $text-secondary;
    margin: 0;
    font-size: 15px;
    font-weight: 500;
    animation: pulse 1.5s infinite ease-in-out;
  }
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}