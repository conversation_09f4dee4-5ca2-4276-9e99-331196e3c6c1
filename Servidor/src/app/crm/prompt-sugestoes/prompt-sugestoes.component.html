<div class="assistente-container">
  <!-- <PERSON><PERSON><PERSON> de Estágio da Venda -->
  <div class="section">
    <div class="section-title">
      <span class="icon-pin">📍</span>
      <h2>Estágio da Venda</h2>
    </div>
    <div class="toggle-buttons">
      <button class="toggle-btn" [class.active]="etapaFunil === 'Prospecção'" (click)="setEtapaFunil('Prospecção')">
        <span class="btn-content">Prospecção</span>
      </button>
      <button class="toggle-btn" [class.active]="etapaFunil === 'Qualificação'" (click)="setEtapaFunil('Qualificação')">
        <span class="btn-content">Qualificação</span>
      </button>
      <button class="toggle-btn" [class.active]="etapaFunil === 'Objeção'" (click)="setEtapaFunil('Objeção')">
        <span class="btn-content">Obje<PERSON></span>
      </button>
      <button class="toggle-btn" [class.active]="etapaFunil === 'Fechamento'" (click)="setEtapaFunil('Fechamento')">
        <span class="btn-content">Fechamento</span>
      </button>
    </div>
  </div>

  <!-- Seletor de Tom da Conversa -->
  <div class="section">
    <div class="section-title">
      <span class="icon-tone">🗣️</span>
      <h2>Tom da Conversa</h2>
    </div>
    <div class="toggle-buttons">
      <button class="toggle-btn" [class.active]="tomConversa === 'Consultivo'" (click)="setTomConversa('Consultivo')">
        <span class="btn-content">Consultivo</span>
      </button>
      <button class="toggle-btn" [class.active]="tomConversa === 'Empático'" (click)="setTomConversa('Empático')">
        <span class="btn-content">Empático</span>
      </button>
      <button class="toggle-btn" [class.active]="tomConversa === 'Técnico'" (click)="setTomConversa('Técnico')">
        <span class="btn-content">Técnico</span>
      </button>
      <button class="toggle-btn" [class.active]="tomConversa === 'Urgente'" (click)="setTomConversa('Urgente')">
        <span class="btn-content">Urgente</span>
      </button>
    </div>
  </div>

  <!-- Sugestões de Mensagens -->
  <div class="section suggestions-section">
    <div class="section-title">
      <span class="icon-chat">💬</span>
      <h2>Respostas para <span class="highlighted-text">{{ tipoObjecao }}</span></h2>
      <span class="contact-info" *ngIf="contextoConversa.contatoAtual">
        <i class="fa fa-user"></i> {{ contextoConversa.contatoAtual }}
      </span>
    </div>

    <div class="suggestions-container">
      <ng-container *ngIf="!carregandoSugestoes; else loadingTemplate">
        <div class="suggestion-card" *ngFor="let sugestao of sugestoes; let i = index" (click)="copiarSugestao(sugestao.texto)">
          <span class="card-number">{{ i + 1 }}</span>
          <p>{{ sugestao.texto }}</p>
          <div class="card-actions">
            <button class="action-btn copy-btn" (click)="$event.stopPropagation(); copiarSugestao(sugestao.texto)" title="Copiar mensagem">
              <i class="fa fa-copy"></i>
            </button>
            <button class="action-btn send-btn" (click)="$event.stopPropagation(); enviarSugestaoParaWhatsApp(sugestao.texto)" title="Enviar para WhatsApp">
              <i class="fa fa-paper-plane"></i>
            </button>
          </div>
        </div>
        
        <div *ngIf="sugestoes.length === 0" class="empty-message">
          <p>Nenhuma sugestão disponível para esta combinação.</p>
        </div>
      </ng-container>

      <ng-template #loadingTemplate>
        <div class="loading-container">
          <div class="spinner"></div>
          <p>Gerando sugestões inteligentes...</p>
        </div>
      </ng-template>
    </div>

    <div class="refresh-button">
      <button class="btn-refresh" (click)="regenerarSugestoes()">
        <i class="fa fa-refresh"></i> Gerar novas sugestões
      </button>
      
      <div class="message-counter" *ngIf="contextoConversa.mensagens && contextoConversa.mensagens.length > 0">
        <span>{{ contextoConversa.mensagens.length }} mensagens na conversa</span>
      </div>
    </div>
  </div>
</div>