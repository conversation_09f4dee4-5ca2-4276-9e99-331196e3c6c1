import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ConversasService, ContextoConversa, MensagemChat } from '../services/conversas.service';
import { SugestaoMensagem, SugestoesService } from '../services/sugestoes.service';

// Ajuda a debugar erros
console.log('Carregando PromptSugestoesComponent');

@Component({
  selector: 'app-prompt-sugestoes',
  templateUrl: './prompt-sugestoes.component.html',
  styleUrls: ['./prompt-sugestoes.component.scss']
})
export class PromptSugestoesComponent implements OnInit, OnDestroy {
  @Input() etapaFunil: string = 'Prospecção';
  @Output() enviarMensagem = new EventEmitter<string>();
  
  contextoConversa: ContextoConversa = { mensagens: [] };
  sugestoes: SugestaoMensagem[] = [];
  carregandoSugestoes: boolean = false;
  tomConversa: string = 'Consultivo';
  tipoObjecao: string = 'Objeção de Preço';
  
  private destroy$ = new Subject<void>();

  constructor(
    private conversasService: ConversasService,
    private sugestoesService: SugestoesService
  ) { }

  ngOnInit(): void {
    console.log('PromptSugestoesComponent - ngOnInit');
    try {
      // Inicializa com algumas sugestões padrão
      this.gerarSugestoes();
      
      // Inscrever para receber atualizações do contexto de conversa
      this.conversasService.contextoConversa$
        .pipe(takeUntil(this.destroy$))
        .subscribe(contexto => {
          console.log('Recebeu atualização de contexto:', contexto);
          if (contexto && contexto.mensagens) {
            this.contextoConversa = contexto;
            this.gerarSugestoes();
          }
        });
    } catch (error) {
      console.error('Erro no ngOnInit do PromptSugestoesComponent:', error);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Gera sugestões com base no contexto da conversa atual e configurações
   */
  gerarSugestoes(): void {
    this.carregandoSugestoes = true;
    
    // Atualiza o contexto com a etapa do funil e tom da conversa
    if (this.contextoConversa) {
      this.contextoConversa.etapaFunil = this.etapaFunil;
      this.contextoConversa.tomConversa = this.tomConversa;
    }
    
    // Determina o tipo de objeção para exibir no título
    if (this.etapaFunil === 'Objeção') {
      this.tipoObjecao = 'Objeção de Preço';
    } else {
      this.tipoObjecao = `${this.etapaFunil} (${this.tomConversa})`;
    }
    
    // Usa o serviço para gerar sugestões baseadas no contexto
    this.sugestoesService.gerarSugestoes(this.contextoConversa)
      .pipe(takeUntil(this.destroy$))
      .subscribe(sugestoes => {
        if (sugestoes && Array.isArray(sugestoes)) {
          this.sugestoes = sugestoes;
        } else {
          this.sugestoes = [];
        }
        this.carregandoSugestoes = false;
      });
  }

  /**
   * Regenera as sugestões
   */
  regenerarSugestoes(): void {
    this.gerarSugestoes();
  }
  
  /**
   * Define a etapa do funil e regenera sugestões
   */
  setEtapaFunil(etapa: string): void {
    this.etapaFunil = etapa;
    this.gerarSugestoes();
  }
  
  /**
   * Define o tom da conversa e regenera sugestões
   */
  setTomConversa(tom: string): void {
    this.tomConversa = tom;
    this.gerarSugestoes();
  }

  /**
   * Copia a sugestão para a área de transferência e a envia para o WhatsApp
   */
  copiarSugestao(sugestao: string): void {
    // Copia para a área de transferência
    navigator.clipboard.writeText(sugestao)
      .then(() => {
        console.log('Texto copiado para a área de transferência');
        
        // Notifica o usuário visualmente
        this.mostrarNotificacaoCopia(sugestao);
        
        // Envia para o WhatsApp
        this.enviarSugestaoParaWhatsApp(sugestao);
      })
      .catch(err => {
        console.error('Erro ao copiar texto: ', err);
      });
  }
  
  /**
   * Mostra uma notificação visual de que o texto foi copiado
   */
  private mostrarNotificacaoCopia(sugestao: string): void {
    // Implementação futura: mostrar um toast ou feedback visual
    console.log('Sugestão copiada:', sugestao);
  }

  /**
   * Envia a sugestão escolhida para o WhatsApp
   */
  enviarSugestaoParaWhatsApp(sugestao: string): void {
    // Emite o evento para o componente pai
    this.enviarMensagem.emit(sugestao);
    
    // Envia mensagem para a extensão do Chrome via postMessage
    window.postMessage({
      tipo: 'ENVIAR_MENSAGEM_WHATSAPP',
      payload: {
        texto: sugestao
      }
    }, '*');
    
    console.log('Solicitou envio de mensagem para WhatsApp:', sugestao);
    
    // Atualiza o contexto local com a mensagem enviada
    const novaMensagem: MensagemChat = {
      texto: sugestao,
      remetente: 'Eu',
      horario: new Date().toLocaleTimeString(),
      tipo: 'saida' as const
    };
    
    if (this.contextoConversa && this.contextoConversa.mensagens) {
      this.contextoConversa.mensagens.push(novaMensagem);
    }
  }
}