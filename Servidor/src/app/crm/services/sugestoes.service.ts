import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ContextoConversa } from './conversas.service';

export interface SugestaoMensagem {
  texto: string;
  palavraChave?: string;
}

@Injectable({
  providedIn: 'root'
})
export class SugestoesService {
  // Mapa de palavras-chave para sugestões de mensagens
  private mapaPalavrasChave: Record<string, string[]> = {
    'preço': [
      'Nossos planos começam a partir de R$99/mês. Posso explicar os detalhes de cada um?',
      'Temos condições especiais neste mês. Posso enviar uma proposta personalizada?'
    ],
    'demo': [
      'Ótimo! Posso agendar uma demonstração para amanhã às 14h?',
      'Podemos fazer uma demonstração hoje mesmo. Qual horário seria melhor para você?'
    ],
    'dúvida': [
      'Entendo sua dúvida. Nosso sistema resolve isso de forma simples, permitindo que você...',
      'É uma dúvida comum. Vou te explicar como funciona:'
    ],
    'concorrente': [
      'Diferente da concorrência, nosso sistema oferece integração completa com WhatsApp e mais recursos de automação.',
      'Nosso diferencial é a facilidade de uso e suporte 24/7, algo que a concorrência não oferece.'
    ],
    'interessado': [
      'Que ótimo! Posso lhe mostrar uma demonstração agora mesmo?',
      'Excelente! Vamos agendar uma reunião para mostrar como podemos atender suas necessidades?'
    ],
    'valor': [
      'O investimento é bem acessível comparado ao retorno que você terá. Posso detalhar melhor?',
      'Nosso sistema se paga em poucos meses pelo aumento de conversão que proporciona.'
    ],
    'problema': [
      'Entendo o desafio que você está enfrentando. Nossa solução foi desenvolvida especificamente para resolver isso.',
      'Muitos clientes tinham esse mesmo problema e conseguiram resultados incríveis com nossa plataforma.'
    ],
    'prazo': [
      'A implementação é rápida, em média 3 dias úteis para ter tudo funcionando.',
      'Podemos iniciar imediatamente após a contratação. Todo o processo leva menos de uma semana.'
    ]
  };

  // Sugestões por etapa do funil
  private sugestoesPorEtapa: Record<string, Record<string, string[]>> = {
    'Prospecção': {
      'Consultivo': [
        'Oi Ricardo! Que legal conhecer você! Vi que tem uma pizzaria. Quantas mesas vocês atendem por dia? Nosso cardápio digital pode multiplicar isso!',
        'Olá! Adoro pizzarias familiares. Vocês já pensaram em como seria se cada mesa pudesse fazer pedidos sem chamar o garçom? Parece ficção científica, né?',
        'Ricardo, parabéns pela pizzaria! Uma pergunta: seus clientes reclamam da demora no atendimento nos fins de semana?'
      ],
      'Empático': [
        'Olá Ricardo! Como está o movimento na pizzaria? Sei que gerenciar o fluxo de clientes pode ser desafiador, especialmente em horários de pico!',
        'Oi! Adoro pizza! Imagino que o maior desafio seja manter a qualidade do atendimento quando a casa está cheia, né?',
        'Olá! Sou apaixonado por negócios familiares como pizzarias. Conte um pouco sobre como começou sua jornada!'
      ]
    },
    'Qualificação': {
      'Consultivo': [
        'Entendi suas necessidades! Nossa solução permite que os clientes façam pedidos diretamente pelo celular escaneando um QR code na mesa. Isso reduz erros em 70% e aumenta a rotatividade das mesas.',
        'Baseado no seu volume de 30 mesas, nossa plataforma pode aumentar sua eficiência em até 40%. O melhor é que você paga apenas uma pequena taxa mensal, sem investimento inicial.',
        'Nossos clientes relatam um aumento médio de 22% no valor do ticket quando usam nosso cardápio digital. Os adicionais são muito mais fáceis de vender!'
      ],
      'Técnico': [
        'Nossa plataforma integra com 95% dos sistemas de PDV do mercado, incluindo o que você usa. A implementação leva apenas 24 horas e não precisa de nenhum hardware adicional.',
        'A sincronização com seu estoque é em tempo real. Quando um item acaba, ele é automaticamente removido do cardápio, evitando frustração dos clientes.',
        'Nossas APIs são abertas e documentadas, permitindo integração com qualquer sistema de gestão ou contabilidade que você já use.'
      ]
    },
    'Objeção': {
      'Consultivo': [
        'Entendo sua preocupação com o custo. A maioria dos nossos clientes recupera o investimento em menos de 45 dias. Uma pizzaria do seu porte economiza em média R$3.200/mês só com redução de erros e otimização da equipe.',
        'O plano mais básico custa menos que um delivery por dia. E você pode fazer um teste gratuito por 30 dias para comprovar o retorno antes de decidir.',
        'Comparado ao custo de contratar mais um garçom (salário + encargos + benefícios), nossa solução é 80% mais econômica e funciona 24/7 sem reclamar!'
      ],
      'Técnico': [
        'Nossa plataforma não exige compra de tablets ou hardware. Os clientes usam o próprio celular, e oferecemos opções flexíveis de pagamento, incluindo mensalidade sem contrato de fidelidade.',
        'Você sabia que restaurantes com sistemas de autoatendimento reduzem o custo operacional em até 30%? Nossos relatórios detalhados permitem identificar todas as economias geradas.',
        'Nossa API processa mais de 500 mil pedidos por dia com 99.99% de uptime garantido em contrato. O suporte técnico está disponível 24/7 sem custo adicional.'
      ]
    },
    'Fechamento': {
      'Consultivo': [
        'Excelente! Vamos formalizar? Posso enviar o contrato hoje mesmo e agendar a implementação para a próxima semana. Assim você já começa a ver resultados antes do fim do mês.',
        'Com base na nossa conversa, recomendo o plano Profissional. Ele inclui todas as funcionalidades que você precisa agora, e você pode fazer upgrade quando sua operação crescer.',
        'Para começarmos, preciso apenas que você preencha este formulário com os dados básicos do restaurante. Nossa equipe fará todo o resto!'
      ],
      'Urgente': [
        'Temos uma promoção especial que termina hoje: 30% de desconto nos 3 primeiros meses. Posso garantir esse valor se fecharmos agora.',
        'Consigo antecipar sua implementação para esta semana se confirmarmos hoje. Assim você já aproveita o movimento do final de semana com o sistema rodando!',
        'Acabei de confirmar com nosso time de implementação que podemos fazer a configuração expressa em 24h se assinarmos hoje. O que acha?'
      ]
    }
  };

  // Sugestões genéricas para quando não há contexto específico
  private sugestoesGenericas: string[] = [
    'Olá! Como posso ajudar você hoje?',
    'Temos várias soluções que podem atender suas necessidades. Podemos conversar mais sobre seu negócio?',
    'Gostaria de agendar uma demonstração para conhecer melhor nossa plataforma?',
    'Estou à disposição para esclarecer qualquer dúvida sobre nossos serviços.',
    'Nossos clientes têm obtido resultados excelentes com nossa solução. Posso compartilhar alguns casos de sucesso?'
  ];

  constructor() { }

  /**
   * Gera sugestões com base no contexto da conversa e parâmetros de configuração
   */
  gerarSugestoes(contexto: ContextoConversa): Observable<SugestaoMensagem[]> {
    console.log('SugestoesService - gerarSugestoes, contexto:', contexto);
    
    try {
      const sugestoes: SugestaoMensagem[] = [];
      const etapa = contexto.etapaFunil || 'Prospecção';
      const tom = contexto.tomConversa || 'Consultivo';
      
      // Verifica se temos sugestões específicas para esta combinação de etapa e tom
      if (this.sugestoesPorEtapa[etapa] && this.sugestoesPorEtapa[etapa][tom]) {
        this.sugestoesPorEtapa[etapa][tom].forEach(texto => {
          sugestoes.push({ texto });
        });
      }
      
      // Se não encontrou sugestões específicas para o tom, tenta usar qualquer tom disponível
      if (sugestoes.length === 0 && this.sugestoesPorEtapa[etapa]) {
        const tomsDisponiveis = Object.keys(this.sugestoesPorEtapa[etapa]);
        if (tomsDisponiveis.length > 0) {
          const tomAlternativo = tomsDisponiveis[0];
          this.sugestoesPorEtapa[etapa][tomAlternativo].forEach(texto => {
            sugestoes.push({ texto });
          });
        }
      }
      
      // Se ainda não tem sugestões, verifica palavras-chave nas mensagens
      if (sugestoes.length === 0 && contexto.mensagens && contexto.mensagens.length > 0) {
        const ultimasMensagens = contexto.mensagens
          .slice(-3)
          .map(msg => msg.texto.toLowerCase());
        
        for (const [palavraChave, respostas] of Object.entries(this.mapaPalavrasChave)) {
          for (const mensagem of ultimasMensagens) {
            if (mensagem.includes(palavraChave)) {
              respostas.forEach(resposta => {
                sugestoes.push({
                  texto: resposta,
                  palavraChave
                });
              });
              break;
            }
          }
        }
      }
      
      // Se ainda não tem sugestões, usa genéricas
      if (sugestoes.length === 0) {
        this.sugestoesGenericas.forEach(texto => {
          sugestoes.push({ texto });
        });
      }
      
      // Limita a 3 sugestões e remove duplicatas
      const sugestoesFiltradas = this.removerDuplicatas(sugestoes).slice(0, 3);
      console.log('Sugestões geradas:', sugestoesFiltradas);
      
      return of(sugestoesFiltradas);
    } catch (error) {
      console.error('Erro ao gerar sugestões:', error);
      // Em caso de erro, retorna algumas sugestões padrão
      return of([
        { texto: 'Olá! Como posso ajudar?' },
        { texto: 'Gostaria de saber mais sobre nossos produtos?' },
        { texto: 'Podemos agendar uma demonstração se preferir.' }
      ]);
    }
  }

  /**
   * Remove sugestões duplicadas com base no texto
   */
  private removerDuplicatas(sugestoes: SugestaoMensagem[]): SugestaoMensagem[] {
    const textosUnicos = new Set<string>();
    return sugestoes.filter(sugestao => {
      if (textosUnicos.has(sugestao.texto)) {
        return false;
      }
      textosUnicos.add(sugestao.texto);
      return true;
    });
  }

  /**
   * Método para implementar a versão com IA (a ser desenvolvido)
   */
  gerarSugestoesComIA(contexto: ContextoConversa): Observable<SugestaoMensagem[]> {
    // Placeholder para futura implementação com IA
    // Por enquanto, usa a versão offline
    return this.gerarSugestoes(contexto);
  }
}