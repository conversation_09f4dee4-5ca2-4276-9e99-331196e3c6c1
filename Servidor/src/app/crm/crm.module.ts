import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatRippleModule } from '@angular/material/core';
import { GridModule } from '@progress/kendo-angular-grid';
import { MaskedTextBoxModule } from '@progress/kendo-angular-inputs';

import { CrmRoutingModule } from './crm-routing.module';
import { CrmHomeComponent } from './crm-home/crm-home.component';
import { TelaCrmLeadsComponent } from '../tela-crm-leads/tela-crm-leads.component';
import { PromptSugestoesComponent } from './prompt-sugestoes/prompt-sugestoes.component';
import { LeadInfoComponent } from './lead-info/lead-info.component';
import { ConversasService } from './services/conversas.service';
import { SugestoesService } from './services/sugestoes.service';
import { LeadService } from './services/lead.service';
import { CrmEmpresaService } from './services/crm-empresa.service';
import { LeadCrudComponent } from './lead-crud/lead-crud.component';
import { CrmEmpresaCrudComponent } from './crm-empresa-crud/crm-empresa-crud.component';
import { NovoLeadComponent } from './novo-lead/novo-lead.component';
import { InstagramDataService } from './services/instagram-data.service';

@NgModule({
  declarations: [
    CrmHomeComponent,
    TelaCrmLeadsComponent,
    PromptSugestoesComponent,
    LeadInfoComponent,
    LeadCrudComponent,
    CrmEmpresaCrudComponent,
    NovoLeadComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatTooltipModule,
    MatRippleModule,
    CrmRoutingModule,
    GridModule,
    MaskedTextBoxModule
  ],
  providers: [
    ConversasService,
    SugestoesService,
    LeadService,
    CrmEmpresaService,
    InstagramDataService
  ]
})
export class CrmModule { }
