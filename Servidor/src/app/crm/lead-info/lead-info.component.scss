// Cores e variáveis
$primary: #3b7ddd;
$primary-dark: #2d6ec0;
$primary-light: #e8f2ff;
$accent: #2ecc71;
$accent-light: #e8f8ed;
$neutral: #4a6583;
$neutral-light: #edf1f7;
$warning: #f39c12;
$danger: #e74c3c;
$gray-light: #f8f9fa;
$gray-lighter: #f2f3f5;
$gray-border: #e0e0e0;
$gray-dark: #495057;
$text-dark: #344767;
$text-secondary: #666;
$radius: 8px;
$shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
$transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

.lead-info-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: white;
  color: $text-dark;
  font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  position: relative;
}

// Header com avatar e nome
.lead-header {
  padding: 20px;
  display: flex;
  align-items: center;
  background-color: $primary-light;
  border-bottom: 1px solid $gray-border;
  position: relative;
}

.lead-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: $primary;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 14px;
  font-size: 22px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.lead-name-details {
  flex: 1;
  overflow: hidden;
  
  h1 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.lead-meta {
  display: flex;
  margin-top: 5px;
  flex-wrap: wrap;
  
  .meta-item {
    font-size: 12px;
    color: $text-secondary;
    margin-right: 12px;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 4px;
      font-size: 10px;
    }
  }
}

.lead-score {
  background-color: $accent;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

// Abas de navegação
.lead-tabs {
  display: flex;
  border-bottom: 1px solid $gray-border;
  background-color: white;
}

.tab-btn {
  flex: 1;
  padding: 12px 8px;
  background: none;
  border: none;
  font-size: 13px;
  color: $text-secondary;
  cursor: pointer;
  transition: $transition;
  position: relative;
  font-weight: 500;
  outline: none;
  
  i {
    margin-right: 4px;
    font-size: 14px;
  }
  
  &:hover {
    color: $primary;
    background-color: rgba(59, 125, 221, 0.05);
  }
  
  &.active {
    color: $primary;
    font-weight: 600;
    
    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 15%;
      width: 70%;
      height: 3px;
      background-color: $primary;
      border-radius: 3px 3px 0 0;
    }
  }
}

// Conteúdo
.lead-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 0 60px 0; // Espaço para o quick-actions no final
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: $gray-lighter;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    
    &:hover {
      background: rgba(0, 0, 0, 0.2);
    }
  }
}

.content-section {
  padding: 16px 20px;
  border-bottom: 1px solid $gray-border;
  
  h2 {
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 16px 0;
    color: $text-dark;
  }
  
  &:last-child {
    border-bottom: none;
  }
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  font-size: 13px;
  align-items: center;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  color: $text-secondary;
  display: flex;
  align-items: center;
  
  i {
    margin-right: 6px;
    width: 14px;
    text-align: center;
    color: $primary;
    opacity: 0.7;
  }
}

.info-value {
  font-weight: 500;
  color: $text-dark;
  text-align: right;
  
  &.highlight {
    color: $primary;
    font-weight: 600;
  }
  
  &.link {
    color: $primary;
    text-decoration: underline;
    cursor: pointer;
  }
}

// Etapas do funil
.etapa-badge {
  padding: 4px 8px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  background-color: $gray-lighter;
  
  &.prospecção {
    background-color: #e3f2fd;
    color: #1976d2;
  }
  
  &.qualificação {
    background-color: #e8f5e9;
    color: #388e3c;
  }
  
  &.objeção {
    background-color: #fff8e1;
    color: #f57f17;
  }
  
  &.fechamento {
    background-color: #e8eaf6;
    color: #3949ab;
  }
}

// Tags e interesses
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 5px;
}

.tag {
  background-color: $primary-light;
  color: $primary;
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 12px;
  font-weight: 500;
}

// Histórico e notas
.propostas-historico, .notas-container {
  font-size: 13px;
  line-height: 1.5;
  color: $text-dark;
  background-color: $gray-lighter;
  padding: 12px;
  border-radius: $radius;
}

.no-data {
  color: $text-secondary;
  font-style: italic;
  font-size: 13px;
  text-align: center;
  padding: 12px;
  background-color: $gray-lighter;
  border-radius: $radius;
}

// Redes sociais
.social-links {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.social-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: $transition;
  
  &:nth-child(1) {
    background: linear-gradient(45deg, #405de6, #5851db, #833ab4, #c13584, #e1306c, #fd1d1d);
  }
  
  &:nth-child(2) {
    background-color: #0077b5;
  }
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
  }
}

.no-socials {
  color: $text-secondary;
  font-style: italic;
  font-size: 13px;
}

// Origem do lead
.origem-info {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.origem-badge {
  background-color: $gray-lighter;
  color: $text-dark;
  font-size: 13px;
  padding: 8px 16px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  
  i {
    color: $primary;
    font-size: 14px;
  }
}

// Campo de notas
.notas-input {
  width: 100%;
  border: 1px solid $gray-border;
  border-radius: $radius;
  padding: 10px;
  margin-top: 15px;
  font-family: inherit;
  font-size: 13px;
  min-height: 80px;
  resize: vertical;
  outline: none;
  transition: $transition;
  
  &:focus {
    border-color: $primary;
    box-shadow: 0 0 0 2px rgba(59, 125, 221, 0.2);
  }
}

.btn-save-note {
  background-color: $primary;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  margin-top: 10px;
  cursor: pointer;
  transition: $transition;
  
  &:hover {
    background-color: $primary-dark;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  }
}

// Quick actions
.quick-actions {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-evenly;
  padding: 10px;
  background-color: white;
  border-top: 1px solid $gray-border;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: $primary-light;
  color: $primary;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  cursor: pointer;
  transition: $transition;
  
  &:hover {
    background-color: $primary;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(59, 125, 221, 0.3);
  }
}