<div class="lead-info-container">
  <div class="lead-header">
    <div class="lead-avatar">
      <i class="fa fa-user"></i>
    </div>
    <div class="lead-name-details">
      <h1>{{ dadosLead.nome || 'Lead sem nome' }}</h1>
      <div class="lead-meta">
        <span class="meta-item" *ngIf="dadosLead.cargo">
          <i class="fa fa-briefcase"></i> {{ dadosLead.cargo }}
        </span>
        <span class="meta-item" *ngIf="dadosLead.empresa">
          <i class="fa fa-building"></i> {{ dadosLead.empresa }}
        </span>
      </div>
    </div>
    
    <div class="lead-score" [ngStyle]="{'background-color': getCorDoScore(dadosLead.scoreLead)}">
      {{ formatarScore(dadosLead.scoreLead) }}
    </div>
  </div>
  
  <!-- Abas de navegação -->
  <div class="lead-tabs">
    <button class="tab-btn" [class.active]="secaoAtiva === 'info'" (click)="alternarSecao('info')">
      <i class="fa fa-info-circle"></i> Informações
    </button>
    <button class="tab-btn" [class.active]="secaoAtiva === 'interacoes'" (click)="alternarSecao('interacoes')">
      <i class="fa fa-comments"></i> Interações
    </button>
    <button class="tab-btn" [class.active]="secaoAtiva === 'notas'" (click)="alternarSecao('notas')">
      <i class="fa fa-sticky-note"></i> Notas
    </button>
  </div>
  
  <!-- Seção de informações básicas -->
  <div class="lead-content" *ngIf="secaoAtiva === 'info'">
    <div class="content-section">
      <h2>Informações Básicas</h2>
      
      <div class="info-item">
        <span class="info-label"><i class="fa fa-phone"></i> Telefone</span>
        <span class="info-value">{{ dadosLead.telefone || 'Não informado' }}</span>
      </div>
      
      <div class="info-item">
        <span class="info-label"><i class="fa fa-envelope"></i> E-mail</span>
        <span class="info-value">{{ dadosLead.email || 'Não informado' }}</span>
      </div>
      
      <div class="info-item">
        <span class="info-label"><i class="fa fa-map-marker"></i> Localização</span>
        <span class="info-value">{{ dadosLead.localizacao || 'Não informado' }}</span>
      </div>
    </div>
    
    <div class="content-section">
      <h2>Sobre a Empresa</h2>
      
      <div class="info-item">
        <span class="info-label"><i class="fa fa-tag"></i> Segmento</span>
        <span class="info-value">{{ dadosLead.segmento || 'Não informado' }}</span>
      </div>
      
      <div class="info-item">
        <span class="info-label"><i class="fa fa-sitemap"></i> Tamanho</span>
        <span class="info-value">{{ dadosLead.tamanhoEmpresa || 'Não informado' }}</span>
      </div>
      
      <div class="info-item" *ngIf="dadosLead.site">
        <span class="info-label"><i class="fa fa-globe"></i> Site</span>
        <span class="info-value link">{{ dadosLead.site }}</span>
      </div>
    </div>

    <!-- Seção de Links -->
    <div class="content-section" *ngIf="dadosLead.links && dadosLead.links.length > 0">
      <h2>Links</h2>

      <div class="row">
        <div class="col-md-6 mb-3" *ngFor="let link of dadosLead.links">
          <div class="card border-left-primary">
            <div class="card-body p-3">
              <div class="d-flex align-items-center">
                <i class="fa mr-3"
                   [ngClass]="getTipoLinkInfo(link.tipo).icone"
                   [style.color]="getTipoLinkInfo(link.tipo).cor"
                   style="font-size: 1.5rem;"></i>
                <div class="flex-grow-1">
                  <h6 class="mb-1">{{ getTipoLinkInfo(link.tipo).texto }}</h6>
                  <p class="mb-1 text-muted small">{{ link.url }}</p>
                  <p class="mb-0 text-muted" style="font-size: 0.8rem;" *ngIf="link.descricao">
                    {{ link.descricao }}
                  </p>
                </div>
                <a [href]="formatarUrlLink(link.url, link.tipo)"
                   target="_blank"
                   class="btn btn-sm btn-outline-primary">
                  <i class="fa fa-external-link-alt"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="content-section">
      <h2>Redes Sociais</h2>
      
      <div class="social-links">
        <a class="social-btn" *ngIf="dadosLead.instagram" [href]="'https://instagram.com/' + dadosLead.instagram?.replace('@', '')" target="_blank">
          <i class="fa fa-instagram"></i>
        </a>
        <a class="social-btn" *ngIf="dadosLead.linkedin" [href]="'https://' + dadosLead.linkedin" target="_blank">
          <i class="fa fa-linkedin"></i>
        </a>
        <span class="no-socials" *ngIf="!dadosLead.instagram && !dadosLead.linkedin">
          Nenhuma rede social registrada
        </span>
      </div>
    </div>
  </div>
  
  <!-- Seção de interações -->
  <div class="lead-content" *ngIf="secaoAtiva === 'interacoes'">
    <div class="content-section">
      <h2>Informações do Funil</h2>
      
      <div class="info-item">
        <span class="info-label"><i class="fa fa-filter"></i> Etapa atual</span>
        <span class="info-value">
          <span class="etapa-badge" [ngClass]="dadosLead.etapaFunil?.toLowerCase() || ''">
            {{ dadosLead.etapaFunil || 'Não definido' }}
          </span>
        </span>
      </div>
      
      <div class="info-item">
        <span class="info-label"><i class="fa fa-calendar-plus-o"></i> Primeiro contato</span>
        <span class="info-value">{{ dadosLead.dataPrimeiroContato || 'Não registrado' }}</span>
      </div>
      
      <div class="info-item">
        <span class="info-label"><i class="fa fa-calendar-check-o"></i> Última interação</span>
        <span class="info-value">{{ dadosLead.ultimaInteracao || 'Não registrado' }}</span>
      </div>
      
      <div class="info-item">
        <span class="info-label"><i class="fa fa-clock-o"></i> Próximo follow-up</span>
        <span class="info-value highlight">{{ dadosLead.proximoFollowUp || 'Não agendado' }}</span>
      </div>
    </div>
    
    <div class="content-section">
      <h2>Interesses</h2>
      
      <div class="tags-container" *ngIf="dadosLead.interessesProdutos && dadosLead.interessesProdutos.length > 0">
        <span class="tag" *ngFor="let interesse of dadosLead.interessesProdutos">
          {{ interesse }}
        </span>
      </div>
      <div class="no-data" *ngIf="!dadosLead.interessesProdutos || dadosLead.interessesProdutos.length === 0">
        Nenhum interesse registrado
      </div>
    </div>
    
    <div class="content-section">
      <h2>Histórico de Propostas</h2>
      
      <div class="propostas-historico" *ngIf="dadosLead.historicoPropostas">
        <p>{{ dadosLead.historicoPropostas }}</p>
      </div>
      <div class="no-data" *ngIf="!dadosLead.historicoPropostas">
        Nenhuma proposta registrada
      </div>
    </div>
  </div>
  
  <!-- Seção de notas -->
  <div class="lead-content" *ngIf="secaoAtiva === 'notas'">
    <div class="content-section">
      <h2>Observações</h2>
      
      <div class="notas-container" *ngIf="dadosLead.observacoes">
        <p>{{ dadosLead.observacoes }}</p>
      </div>
      <div class="no-data" *ngIf="!dadosLead.observacoes">
        Nenhuma observação registrada
      </div>
      
      <textarea class="notas-input" placeholder="Adicionar nova observação..."></textarea>
      <button class="btn-save-note">Salvar observação</button>
    </div>
    
    <div class="content-section">
      <h2>Origem do Lead</h2>
      
      <div class="origem-info">
        <div class="origem-badge">
          <i class="fa" 
             [ngClass]="{
               'fa-globe': dadosLead.origemLead === 'Site', 
               'fa-instagram': dadosLead.origemLead === 'Instagram', 
               'fa-whatsapp': dadosLead.origemLead === 'WhatsApp',
               'fa-user': dadosLead.origemLead === 'Indicação',
               'fa-search': !dadosLead.origemLead
             }">
          </i>
          <span>{{ dadosLead.origemLead || 'Desconhecida' }}</span>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Quick actions -->
  <div class="quick-actions">
    <button class="action-btn" title="Ligar">
      <i class="fa fa-phone"></i>
    </button>
    <button class="action-btn" title="E-mail">
      <i class="fa fa-envelope"></i>
    </button>
    <button class="action-btn" title="Agendar tarefa">
      <i class="fa fa-calendar-plus-o"></i>
    </button>
    <button class="action-btn" title="Adicionar nota">
      <i class="fa fa-sticky-note"></i>
    </button>
  </div>
</div>