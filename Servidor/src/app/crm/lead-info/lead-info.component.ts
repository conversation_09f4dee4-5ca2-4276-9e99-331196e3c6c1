import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ConversasService, ContextoConversa, DadosLead } from '../services/conversas.service';

@Component({
  selector: 'app-lead-info',
  templateUrl: './lead-info.component.html',
  styleUrls: ['./lead-info.component.scss']
})
export class LeadInfoComponent implements OnInit, OnDestroy {
  dadosLead: DadosLead = {};
  contextoConversa: ContextoConversa = { mensagens: [] };
  secaoAtiva: 'info' | 'interacoes' | 'notas' = 'info';
  
  private destroy$ = new Subject<void>();

  constructor(private conversasService: ConversasService) { }

  ngOnInit(): void {
    // Inscrever para receber atualizações do contexto de conversa
    this.conversasService.contextoConversa$
      .pipe(takeUntil(this.destroy$))
      .subscribe(contexto => {
        console.log('LeadInfo recebeu atualização de contexto:', contexto);
        this.contextoConversa = contexto;
        if (contexto.dadosLead) {
          this.dadosLead = contexto.dadosLead;
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Alterna entre as seções de informações do lead
   */
  alternarSecao(secao: 'info' | 'interacoes' | 'notas'): void {
    this.secaoAtiva = secao;
  }
  
  /**
   * Retorna uma cor baseada no score do lead
   */
  getCorDoScore(score: number | undefined): string {
    if (!score) return '#999'; // Cinza para score indefinido
    
    if (score >= 80) return '#2ecc71'; // Verde para score alto
    if (score >= 50) return '#f39c12'; // Amarelo para score médio
    return '#e74c3c'; // Vermelho para score baixo
  }
  
  /**
   * Formata o score para exibição
   */
  formatarScore(score: number | undefined): string {
    if (!score && score !== 0) return 'N/A';
    return `${score}%`;
  }

  /**
   * Obter informações do tipo de link
   */
  getTipoLinkInfo(tipo: string): any {
    const tiposLink = [
      { valor: 'Ifood', texto: 'iFood', icone: 'fa-utensils', cor: '#ea1d2c' },
      { valor: 'Site do Cardápio', texto: 'Site do Cardápio', icone: 'fa-list-alt', cor: '#007bff' },
      { valor: 'Concorrente', texto: 'Concorrente', icone: 'fa-exclamation-triangle', cor: '#ff6b35' },
      { valor: 'Reservas', texto: 'Reservas', icone: 'fa-calendar-check', cor: '#28a745' },
      { valor: 'WhatsApp', texto: 'WhatsApp', icone: 'fa-whatsapp', cor: '#25d366' },
      { valor: 'Localização', texto: 'Localização', icone: 'fa-map-marker-alt', cor: '#dc3545' },
      { valor: 'Site', texto: 'Site', icone: 'fa-globe', cor: '#6c757d' },
      { valor: 'Instagram', texto: 'Instagram', icone: 'fa-instagram', cor: '#e4405f' }
    ];

    return tiposLink.find(t => t.valor === tipo) || {
      texto: tipo,
      icone: 'fa-link',
      cor: '#6c757d'
    };
  }

  /**
   * Formatar URL do link
   */
  formatarUrlLink(url: string, tipo: string): string {
    if (!url) return '';

    // Formatações específicas por tipo
    switch (tipo) {
      case 'WhatsApp':
        if (url.includes('wa.me') || url.includes('whatsapp.com')) {
          return url;
        }
        const numeroLimpo = url.replace(/\D/g, '');
        return numeroLimpo.length >= 10 ? `https://wa.me/55${numeroLimpo}` : url;

      case 'Localização':
        if (url.includes('maps.google.com') || url.includes('goo.gl/maps')) {
          return url;
        }
        return `https://maps.google.com/maps?q=${encodeURIComponent(url)}`;

      default:
        return url.startsWith('http') ? url : `https://${url}`;
    }
  }
}