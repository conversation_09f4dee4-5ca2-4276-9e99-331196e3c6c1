<div class="container-fluid">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h3><i class="fa fa-building"></i> Empresas CRM</h3>
    <button class="btn btn-primary" (click)="nova()" *ngIf="!modoEdicao">
      <i class="fa fa-plus"></i> Nova Empresa
    </button>
  </div>

  <!-- Formulário -->
  <div class="card mb-4" *ngIf="modoEdicao">
    <div class="card-header">
      <h5 class="mb-0">
        <i class="fa fa-edit"></i> 
        {{ empresaSelecionada.id ? 'Editar Empresa' : 'Nova Empresa' }}
      </h5>
    </div>
    <div class="card-body">
      <form #empresaForm="ngForm" (ngSubmit)="salvar()">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="nome"><strong>Nome da Empresa *</strong></label>
              <input type="text" 
                     id="nome" name="nome" 
                     class="form-control" 
                     [(ngModel)]="empresaSelecionada.nome" 
                     required
                     placeholder="Ex: MeuCardápio LTDA">
            </div>
          </div>
          
          <div class="col-md-3">
            <div class="form-group">
              <label for="cnpj"><strong>CNPJ</strong></label>
              <input type="text" 
                     id="cnpj" name="cnpj" 
                     class="form-control" 
                     [(ngModel)]="empresaSelecionada.cnpj"
                     (ngModelChange)="formatarCnpj()"
                     placeholder="00.000.000/0001-00">
            </div>
          </div>
          
          <div class="col-md-3">
            <div class="form-group">
              <label for="ativa"><strong>Status</strong></label>
              <select id="ativa" name="ativa" 
                      class="form-control" 
                      [(ngModel)]="empresaSelecionada.ativa">
                <option [value]="true">Ativa</option>
                <option [value]="false">Inativa</option>
              </select>
            </div>
          </div>
        </div>
        
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="telefone"><strong>Telefone</strong></label>
              <input type="text" 
                     id="telefone" name="telefone" 
                     class="form-control" 
                     [(ngModel)]="empresaSelecionada.telefone"
                     placeholder="(11) 99999-9999">
            </div>
          </div>
          
          <div class="col-md-6">
            <div class="form-group">
              <label for="email"><strong>E-mail</strong></label>
              <input type="email" 
                     id="email" name="email" 
                     class="form-control" 
                     [(ngModel)]="empresaSelecionada.email"
                     placeholder="<EMAIL>">
            </div>
          </div>
        </div>
        
        <div class="row">
          <div class="col-12">
            <div class="form-group">
              <label for="endereco"><strong>Endereço</strong></label>
              <textarea id="endereco" name="endereco" 
                        class="form-control" 
                        [(ngModel)]="empresaSelecionada.endereco" 
                        rows="2"
                        placeholder="Endereço completo..."></textarea>
            </div>
          </div>
        </div>

        <div class="d-flex justify-content-end">
          <button type="button" class="btn btn-secondary mr-2" (click)="cancelar()">
            <i class="fa fa-times"></i> Cancelar
          </button>
          <button type="submit" class="btn btn-success" [disabled]="empresaForm.invalid">
            <i class="fa fa-save"></i> Salvar
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Grid de Listagem -->
  <div class="card" *ngIf="!modoEdicao">
    <div class="card-header">
      <h5 class="mb-0"><i class="fa fa-list"></i> Lista de Empresas</h5>
    </div>
    <div class="card-body">
      <kendo-grid [data]="empresas" 
                  [loading]="carregando" 
                  [height]="500"
                  [pageable]="true"
                  [sortable]="true"
                  [filterable]="true">
        
        <kendo-grid-column field="nome" title="Nome" width="250">
          <ng-template kendoGridCellTemplate let-dataItem>
            <strong>{{ dataItem.nome }}</strong>
          </ng-template>
        </kendo-grid-column>
        
        <kendo-grid-column field="cnpj" title="CNPJ" width="150">
        </kendo-grid-column>
        
        <kendo-grid-column field="telefone" title="Telefone" width="130">
        </kendo-grid-column>
        
        <kendo-grid-column field="email" title="E-mail" width="200">
        </kendo-grid-column>
        
        <kendo-grid-column field="ativa" title="Status" width="80">
          <ng-template kendoGridCellTemplate let-dataItem>
            <span class="badge" 
                  [class]="dataItem.ativa ? 'badge-success' : 'badge-secondary'">
              {{ dataItem.ativa ? 'Ativa' : 'Inativa' }}
            </span>
          </ng-template>
        </kendo-grid-column>
        
        <kendo-grid-column title="Ações" width="120">
          <ng-template kendoGridCellTemplate let-dataItem>
            <button class="btn btn-sm btn-outline-primary mr-1" 
                    (click)="editar(dataItem)"
                    title="Editar">
              <i class="fa fa-edit"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger" 
                    (click)="remover(dataItem.id)"
                    title="Remover">
              <i class="fa fa-trash"></i>
            </button>
          </ng-template>
        </kendo-grid-column>
      </kendo-grid>
    </div>
  </div>
</div> 