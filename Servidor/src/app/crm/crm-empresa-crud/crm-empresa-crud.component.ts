import { Component, OnInit } from '@angular/core';
import { CrmEmpresaService } from '../services/crm-empresa.service';

@Component({
  selector: 'app-crm-empresa-crud',
  templateUrl: './crm-empresa-crud.component.html',
  styleUrls: ['./crm-empresa-crud.component.scss']
})
export class CrmEmpresaCrudComponent implements OnInit {
  empresas: any[] = [];
  empresaSelecionada: any = {};
  carregando = false;
  modoEdicao = false;

  constructor(private crmEmpresaService: CrmEmpresaService) {}

  ngOnInit(): void {
    this.listar();
  }

  listar(): void {
    this.carregando = true;
    this.crmEmpresaService.liste({ inicio: 0, total: 100 }).then((resp) => {
      this.empresas = resp.data || resp;
      this.carregando = false;
    }).catch(() => this.carregando = false);
  }

  nova(): void {
    this.empresaSelecionada = {
      ativa: true
    };
    this.modoEdicao = true;
  }

  editar(empresa: any): void {
    this.empresaSelecionada = { ...empresa };
    this.modoEdicao = true;
  }

  cancelar(): void {
    this.empresaSelecionada = {};
    this.modoEdicao = false;
  }

  salvar(): void {
    this.crmEmpresaService.salveEmpresa(this.empresaSelecionada).then(() => {
      this.cancelar();
      this.listar();
    }).catch((erro) => {
      alert('Erro ao salvar: ' + erro);
    });
  }

  remover(id: number): void {
    if (!confirm('Deseja realmente remover esta empresa?')) return;
    this.crmEmpresaService.removaEmpresa(id).then(() => this.listar());
  }

  // Formatação de CNPJ
  formatarCnpj(): void {
    if (this.empresaSelecionada.cnpj) {
      let cnpj = this.empresaSelecionada.cnpj.replace(/\D/g, '');
      cnpj = cnpj.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
      this.empresaSelecionada.cnpj = cnpj;
    }
  }
} 